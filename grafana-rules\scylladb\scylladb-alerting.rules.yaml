apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: scylladb-folder
spec:
  allowCrossNamespaceImport: true
  title: scylladb
  instanceSelector:
    matchLabels:
      dashboards: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: scylladb-alerting-rules-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: scylladb-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana
  interval: 1m
  rules:
  - uid: scyllacqlnonprepared
    title: ScyllaCqlNonPrepared
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: cql:non_prepared > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      department: DBA
      component: ScyllaDB
      advisor: cqlOptimization
      dashboard: cql
    annotations:
      description: Some queries are non-prepared
      summary: Non prepared statements
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllacql:non-paged-no-system
    title: ScyllaCql:non_paged_no_system
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: cql:non_paged > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      department: DBA
      component: ScyllaDB
      advisor: cqlOptimization
      dashboard: cql
      status: '1'
    annotations:
      description: Some SELECT queries are non-paged
      summary: Non paged statements
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllacqlnotokenaware
    title: ScyllaCqlNoTokenAware
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: cql:non_token_aware > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      department: DBA
      component: ScyllaDB
      advisor: cqlOptimization
      dashboard: cql
    annotations:
      description: Some queries are not token-aware
      summary: Non token aware statements
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllacqlallowfiltering
    title: ScyllaCqlAllowFiltering
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: cql:allow_filtering > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      department: DBA
      component: ScyllaDB
      advisor: cqlOptimization
      dashboard: cql
    annotations:
      description: Some queries use ALLOW FILTERING
      summary: Allow filtering queries
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllacqlclany
    title: ScyllaCqlCLAny
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: cql:any_queries > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      department: DBA
      component: ScyllaDB
      advisor: cqlOptimization
      dashboard: cql
    annotations:
      description: 'Some queries use Consistency Level: ANY'
      summary: Non prepared statements
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllacqlclall
    title: ScyllaCqlCLAll
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: cql:all_queries > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      department: DBA
      component: ScyllaDB
      advisor: cqlOptimization
      dashboard: cql
    annotations:
      description: 'Some queries use Consistency Level: ALL'
      summary: Non prepared statements
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllanonbalancedcqltraffic
    title: ScyllaNonBalancedcqlTraffic
    condition: C
    for: 3m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: abs(rate(scylla_cql_updates{conditional="no"}[1m]) - scalar(avg(rate(scylla_cql_updates{conditional="no"}[1m]))))/scalar(stddev(rate(scylla_cql_updates{conditional="no"}[1m]))+100)
          > 2
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      department: DBA
      component: ScyllaDB
      status: '1'
      advisor: balanced
      dashboard: cql
    annotations:
      description: CQL queries are not balanced among shards {{ $labels.instance }}
        shard {{ $labels.shard }}
      summary: CQL queries are not balanced
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllanodelocalerrors
    title: ScyllaNodeLocalErrors
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(scylla_errors:local_failed) by (cluster, instance) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      department: DBA
      component: ScyllaDB
      advisor: operationError
      dashboard: scylla-detailed
    annotations:
      description: Some operation failed at the replica side
      summary: Replica side Level error
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllanodeioerrors
    title: ScyllaNodeIOErrors
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(rate(scylla_reactor_aio_errors[60s])) by (cluster, instance) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      department: DBA
      component: ScyllaDB
      advisor: operationError
      dashboard: OS-master
      alert_type: db-disk-alerts
    annotations:
      description: IO Errors can indicate a node with a faulty disk {{ $labels.instance
        }}
      summary: IO Disk Error
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllanodeclerrors
    title: ScyllaNodeCLErrors
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(errors:operation_unavailable) by (cluster) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      department: DBA
      component: ScyllaDB
      advisor: operationError
      dashboard: scylla-detailed
    annotations:
      description: Some operation failed due to consistency level
      summary: Consistency Level error
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllapreparedcacheeviction
    title: ScyllaPreparedCacheEviction
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(rate(scylla_cql_prepared_cache_evictions[2m])) by (cluster) + sum(rate(scylla_cql_authorized_prepared_statements_cache_evictions[2m]))
          by (cluster) > 100
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      department: DBA
      component: ScyllaDB
      advisor: preparedEviction
      dashboard: scylla-detailed
    annotations:
      description: The prepared-statement cache is being continuously evicted, which
        could indicate a problem in your prepared-statement usage logic.
      summary: Prepared cache eviction
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllaheavycompaction
    title: ScyllaHeavyCompaction
    condition: C
    for: 20m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: max(scylla_scheduler_shares{group="compaction"}) by (cluster) >= 1000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      department: DBA
      component: ScyllaDB
      advisor: heavyCompaction
      dashboard: scylla-detailed
    annotations:
      description: Compaction load increases to a level it can interfere with the
        system behaviour. If this persists set the compaction share to a static level.
      summary: Heavy compaction load
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllashedrequests
    title: ScyllaShedRequests
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: max(sum(rate(scylla_transport_requests_shed[60s])) by (instance,cluster)/sum(rate(scylla_transport_requests_served{}[60s]))
          by (instance, cluster)) by(cluster) > 0.01
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      department: DBA
      component: ScyllaDB
      advisor: systemOverload
      dashboard: scylla-detailed
    annotations:
      description: More than 1% of the requests got shed, this is an indication of
        an overload, consider system resize.
      summary: System is overloaded
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllacappedtombstone
    title: ScyllaCappedTombstone
    condition: C
    for: 1m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: changes(scylla_sstables_capped_tombstone_deletion_time[1h]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      department: DBA
      component: ScyllaDB
      advisor: cappedTombstone
      dashboard: scylla-detailed
    annotations:
      description: Tombstone delete time was set too far in the future and was capped
      summary: Tobmstone delete time is capped
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllainstancedown
    title: ScyllaInstanceDown
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: up{job=~".*scylla.*",endpoint="metrics"} == 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: ScyllaDB
    annotations:
      description: Instance {{ $labels.instance }} has been down for more than 5 minutes.
      summary: Instance down
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllainstancedowntransportrequests
    title: ScyllaInstanceDownTransportRequests
    condition: C
    for: 10m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum by (instance) (up{endpoint="metrics",job=~"scylla-db.*"} > 0) unless
          sum by (instance) (scylla_transport_requests_served{shard="0"})
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: ScyllaDB
    annotations:
      description: Instance {{ $labels.instance }} instance has been down for more
        than 10 minutes.
      summary: Instance down
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllainstancedownnodeoperationmode
    title: ScyllaInstanceDownNodeOperationMode
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: scylla_node_operation_mode > 3
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: ScyllaDB
    annotations:
      description: Instance {{ $labels.instance }} instance has been down for more
        than 5 minutes.
      summary: Instance down
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scylladiskfullwarning
    title: ScyllaDiskFullWarning
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: node_filesystem_avail_bytes{mountpoint="/scylladb", job="scylla-db-hosts-sm"}
          / node_filesystem_size_bytes{mountpoint="/scylladb", job="scylla-db-hosts-sm"}
          * 100 < 20
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DBA
      component: ScyllaDB
      alert_type: db-disk-alerts
    annotations:
      description: 'Scylla instance {{ $labels.instance }} has less than 20% free
        disk space. Current free disk space left: {{ $value }}%'
      summary: Instance low disk space warning
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scylladiskfullcritical
    title: ScyllaDiskFullCritical
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: node_filesystem_avail_bytes{mountpoint="/scylladb", job="scylla-db-hosts-sm"}
          / node_filesystem_size_bytes{mountpoint="/scylladb", job="scylla-db-hosts-sm"}
          * 100 < 10
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: ScyllaDB
      alert_type: db-disk-alerts
    annotations:
      description: 'Scylla instance {{ $labels.instance }} has less than 10% free
        disk space. Current free disk space left: {{ $value }}%'
      summary: Instance low disk space critical
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllanocql
    title: ScyllaNoCql
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: scylla_manager_healthcheck_cql_status == -1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      department: DBA
      component: ScyllaDB
    annotations:
      description: Host {{ $labels.host }} has denied CQL connection for more than
        30 seconds.
      summary: Instance no CQL connection
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllahighlatencieswlatencyp95
    title: ScyllaHighLatenciesWLatencyp95
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: wlatencyp95{by="instance"} > 100000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      department: DBA
      component: ScyllaDB
    annotations:
      description: Instance {{ $labels.instance }} has 95% high latency for more than
        5 minutes.
      summary: Instance High Write Latency
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllahighlatencieswlatencya
    title: ScyllaHighLatenciesWLatencya
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: wlatencya{by="instance"} > 10000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      department: DBA
      component: ScyllaDB
    annotations:
      description: Instance {{ $labels.instance }} has average high latency for more
        than 5 minutes.
      summary: Instance High Write Latency
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllahighlatenciesrlatetencyp95
    title: ScyllaHighLatenciesRLatetencyp95
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: rlatencyp95{by="instance"} > 100000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      department: DBA
      component: ScyllaDB
    annotations:
      description: Instance {{ $labels.instance }} has 95% high latency for more than
        5 minutes.
      summary: Instance High Read Latency
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllahighlatencies
    title: ScyllaHighLatencies
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: rlatencya{by="instance"} > 10000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      department: DBA
      component: ScyllaDB
    annotations:
      description: Instance {{ $labels.instance }} has average high latency for more
        than 5 minutes.
      summary: Instance High Read Latency
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllabackupfailed
    title: ScyllaBackupFailed
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: (sum(scylla_manager_scheduler_run_total{type=~"backup", status="ERROR"})
          or vector(0)) - (sum(scylla_manager_scheduler_run_total{type=~"backup",
          status="ERROR"} offset 3m) or vector(0)) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DBA
      component: ScyllaDB
    annotations:
      description: Scylla Backup failed
      summary: Backup task failed
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllarepairfailed
    title: ScyllaRepairFailed
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: (sum(scylla_manager_scheduler_run_total{type=~"repair", status="ERROR"})
          or vector(0)) - (sum(scylla_manager_scheduler_run_total{type=~"repair",
          status="ERROR"} offset 3m) or vector(0)) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DBA
      component: ScyllaDB
    annotations:
      description: Scylla Repair failed
      summary: Repair task failed
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllarestart
    title: ScyllaRestart
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: resets(scylla_gossip_heart_beat[1h]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      department: DBA
      component: ScyllaDB
    annotations:
      description: Instance {{ $labels.instance }} restarted
      summary: Node restarted
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllaoomkill
    title: ScyllaOOMKill
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: changes(node_vmstat_oom_kill{job=~"scylla.*"}[30m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DBA
      component: ScyllaDB
    annotations:
      description: A process was terminated on Instance {{ $labels.instance }}
      summary: OOM Kill. A process was terminated.
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllatoomanyfilesinfo
    title: ScyllaTooManyFilesInfo
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: (node_filesystem_files{mountpoint="/scylladb", job="scylla-db-hosts-sm"}
          - node_filesystem_files_free{mountpoint="/scylladb", job="scylla-db-hosts-sm"})
          / on(instance) group_left count(scylla_reactor_cpu_busy_ms) by (instance)
          > 20000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      department: DBA
      component: ScyllaDB
      alert_type: db-disk-alerts
    annotations:
      description: 'Over 20k open files in /scylladb-data per shard on Instance: {{
        $labels.instance }}'
      summary: There are over 20K open files per shard
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllatoomanyfileswarning
    title: ScyllatooManyFilesWarning
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: (node_filesystem_files{mountpoint="/scylladb", job="scylla-db-hosts-sm"}
          - node_filesystem_files_free{mountpoint="/scylladb", job="scylla-db-hosts-sm"})
          / on(instance) group_left count(scylla_reactor_cpu_busy_ms) by (instance)
          > 30000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DBA
      component: ScyllaDB
      alert_type: db-disk-alerts
    annotations:
      description: 'Over 30k open files in /scylladb-data per shard on Instance: {{
        $labels.instance }}. Current value: {{ $value }}'
      summary: There are over 30K open files per shard
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllatoomanyfilescritical
    title: ScyllaTooManyFilesCritical
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: (node_filesystem_files{mountpoint="/scylladb", job="scylla-db-hosts-sm"}
          - node_filesystem_files_free{mountpoint="/scylladb", job="scylla-db-hosts-sm"})
          / on(instance) group_left count(scylla_reactor_cpu_busy_ms) by (instance)
          > 40000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: ScyllaDB
      alert_type: db-disk-alerts
    annotations:
      description: 'Over 40k open files in /scylladb-data per shard on Instance: {{
        $labels.instance }}. Current value: {{ $value }}'
      summary: There are over 40K open files per shard
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllanodeinjoinmodeinfo
    title: ScyllanodeInJoinModeInfo
    condition: C
    for: 5h
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: scylla_node_operation_mode == 2
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      department: DBA
      component: ScyllaDB
    annotations:
      description: Node {{ $labels.instance }} in Joining mode for 5 hours
      summary: Node is in Joining mode for 5 hours
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllanodeinjoinmodewarning
    title: ScyllanodeInJoinModeWarning
    condition: C
    for: 24h
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: scylla_node_operation_mode == 2
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DBA
      component: ScyllaDB
    annotations:
      description: Node {{ $labels.instance }} in Joining mode for 1 day
      summary: Node is in Joining mode for 1 day
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllasplitbrain
    title: ScyllasplitBrain
    condition: C
    for: 10m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(scylla_gossip_live) by (cluster) < (count(scylla_node_operation_mode==3)
          by (cluster)  -1) * count(scylla_gossip_live) by (cluster)
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DBA
      component: ScyllaDB
    annotations:
      description: Some nodes in the cluster {{ $labels.cluster }} do not see all
        of the other live nodes
      summary: Cluster in a split-brain mode
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: scyllabloomfiltersize
    title: ScyllabloomFilterSize
    condition: C
    for: 10m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: scylla_sstables_bloom_filter_memory_size/scylla_memory_total_memory
          > 0.2
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DBA
      component: ScyllaDB
    annotations:
      description: Bloom filter size in node {{ $labels.instance }} is equal to {{
        $value }}. Update bloom_filter_fp_chance
      summary: The bloom filter takes too much memory
    noDataState: NoData
    execErrState: Error
    isPaused: false
