apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: clickhouse-folder
spec:
  allowCrossNamespaceImport: true
  title: clickhouse
  instanceSelector:
    matchLabels:
      dashboards: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: clickhouse-delay-exporter-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: clickhouse-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana
  interval: 1m
  rules:
  - uid: clickhousedelayscrapedown
    title: ClickhouseDelayScrapeDown
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: clickhouse_delay_scrape_status == 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: clickhouse
      department: Tableau
    annotations:
      summary: Clickhouse delay scrape failed.
      description: Check the clickhouse connections
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhousedelayalert
    title: ClickhouseDelayAlert
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: clickhouse_delay_alert == 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: clickhouse
      department: Tableau
    annotations:
      summary: Clickhouse table delay exceeded expected threshold
      description: Check the related table
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
