apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: dataguard-folder
spec:
  allowCrossNamespaceImport: true
  title: dataguard
  instanceSelector:
    matchLabels:
      dashboards: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: dataguard-rules-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: dataguard-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana
  interval: 1m
  rules:
  - uid: dataguardrestart
    title: DataguardRestart
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: systemd_unit_state{name="dataguard.service",state="active",type="service"}
          != 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: infrastructure
      department: DevOps
    annotations:
      summary: DataGuard service state was inactive
      description: Service {{ $labels.name }} has been restarted
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
