apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: crm-folder
spec:
  allowCrossNamespaceImport: true
  title: crm
  instanceSelector:
    matchLabels:
      dashboards: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: outbound-proxy-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: crm-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana
  interval: 1m
  rules:
  - uid: seamlesswalletoutboundproxyerrors
    title: SeamlessWalletOutboundProxyErrors
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(execution_failed_total{job=~"ews-outbound-proxy-api|ews-outbound-message-router-service|ews-outbound-message-processor-service"}[1m])
          > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: service
      department: crm
    annotations:
      summary: Seamless Wallet (Outbound Proxy) Errors
      description: Seamless Wallet (Outbound Proxy) Errors - {{ $labels.job }} on
        pod {{ $labels.pod }} for action {{ $labels.action }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: seamlesswalletoutboundproxydeadletter
    title: SeamlessWalletOutboundProxyDeadLetter
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: cache_items_count{job="ews-outbound-message-router-service", cache="DeadLetterKafkaCache"}  >
          10
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: service
      department: crm
    annotations:
      summary: Seamless Wallet (Outbound Proxy) - Too Many Dead Letter Messages
      description: Seamless Wallet (Outbound Proxy) - Too Many Dead Letter Messages
        on pod {{ $labels.pod }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
