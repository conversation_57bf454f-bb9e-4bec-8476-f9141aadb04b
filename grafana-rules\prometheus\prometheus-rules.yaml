apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: prometheus-folder
spec:
  allowCrossNamespaceImport: true
  title: prometheus
  instanceSelector:
    matchLabels:
      dashboards: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: prometheus-self-monitoring-rules-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: prometheus-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana
  interval: 1m
  rules:
  - uid: prometheusjobstatus
    title: PrometheusJobStatus
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: up{job="kube-prometheus-stack-prometheus"} == 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus job is down
      description: "A Prometheus job status down.\n  VALUE = {{ $value }}\b Job =\
        \ {{ $labels.instance }} \n  Instance =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheusnodeexporterservice
    title: PrometheusNodeExporterService
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: up{service="kube-prometheus-stack-prometheus-node-exporter"} == 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus node exporter service status down.
      description: "A Prometheus target has disappeared. An exporter might be crashed.\n\
        \  VALUE = {{ $value }}\n  Instance =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheusbuildinfostatus
    title: PrometheusBuildInfoStatus
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: prometheus_build_info != 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus build info problem
      description: "Prometheus build info status not ok\n  VALUE = {{ $value }}\n\
        \  Instance =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheusoperatorbuildinfostatus
    title: PrometheusOperatorBuildInfoStatus
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: prometheus_operator_build_info != 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus operator build info problem
      description: "Prometheus operator build info status not ok\n  VALUE = {{ $value\
        \ }}\n  Instance =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheusalltargetsmissing
    title: PrometheusAllTargetsMissing
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: sum by (job) (up{service="kube-prometheus-stack-prometheus-node-exporter"})
          == 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus all targets missing
      description: "A Prometheus job does not have living target anymore.\n  VALUE\
        \ = {{ $value }}\b Job = {{ $labels.instance }} \n  Instance =  {{ $labels.instance\
        \ }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheusconfigurationreloadfailure
    title: PrometheusConfigurationReloadFailure
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: prometheus_config_last_reload_successful != 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus configuration reload failure
      description: "Prometheus configuration reload error\n  VALUE = {{ $value }}\n\
        \ Instance =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheustoomanyrestarts
    title: PrometheusTooManyRestarts
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: changes(process_start_time_seconds{job=~"prometheus|pushgateway|alertmanager"}[15m])
          > 2
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus too many restarts
      description: "Prometheus has restarted more than twice in the last 15 minutes.\
        \ It might be crashlooping.\n Job = {{ $labels.job }}\n  VALUE = {{ $value\
        \ }}\n Instance =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheusalertmanagerjobmissing
    title: PrometheusAlertmanagerJobMissing
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: absent(up{service="kube-prometheus-stack-alertmanager"})
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus AlertManager service missing
      description: "A Prometheus AlertManager job has disappeared\n  VALUE = {{ $value\
        \ }}\n Instance =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheus-alertmgr-config-fail
    title: PrometheusAlertmanagerConfigurationReloadFailure
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: alertmanager_config_last_reload_successful != 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus AlertManager configuration reload failure
      description: "AlertManager configuration reload error\n  VALUE = {{ $value }}\n\
        \ Instance =  {{ $labels.instance }}\n Pod = {{ $labels.pod }} "
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheusalertmanagerconfignotsynced
    title: PrometheusAlertmanagerConfigNotSynced
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: count(count_values("config_hash", alertmanager_config_hash)) > 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus AlertManager config not synced
      description: "Configurations of AlertManager cluster instances are out of sync\n\
        \  VALUE = {{ $value }}\n Instance =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheusnotconnectedtoalertmanager
    title: PrometheusNotConnectedToAlertmanager
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: prometheus_notifications_alertmanagers_discovered < 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus not connected to alertmanager
      description: "Prometheus cannot connect the alertmanager\n  VALUE = {{ $value\
        \ }}\n Instance =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheusruleevaluationfailures
    title: PrometheusRuleEvaluationFailures
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: increase(prometheus_rule_evaluation_failures_total[3m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus rule evaluation failures
      description: "Prometheus encountered {{ $value }} rule evaluation failures,\
        \ leading to potentially ignored alerts.\n  VALUE = {{ $value }}\n Instance\
        \ =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheustemplatetextexpansionfailures
    title: PrometheusTemplateTextExpansionFailures
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: increase(prometheus_template_text_expansion_failures_total[3m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus template text expansion failures
      description: "Prometheus encountered {{ $value }} template text expansion failures\n\
        \  VALUE = {{ $value }}\n Instance =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheusruleevaluationslow
    title: PrometheusRuleEvaluationSlow
    condition: A
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: prometheus_rule_group_last_duration_seconds > prometheus_rule_group_interval_seconds
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus rule evaluation slow
      description: "Prometheus rule evaluation took more time than the scheduled interval.\
        \ It indicates a slower storage backend access or too complex query.\n  VALUE\
        \ = {{ $value }}\n Instance =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheusnotificationsbacklog
    title: PrometheusNotificationsBacklog
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: min_over_time(prometheus_notifications_queue_length[10m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus notifications backlog
      description: "The Prometheus notification queue has not been empty for 10 minutes\n\
        \  VALUE = {{ $value }}\n Instance =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheus-alertmgr-notif-fail
    title: PrometheusAlertmanagerNotificationFailing
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: rate(alertmanager_notifications_failed_total[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus AlertManager notification failing
      description: "Alertmanager is failing sending notifications\n  VALUE = {{ $value\
        \ }}\n Instance =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheustargetempty
    title: PrometheusTargetEmpty
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: prometheus_sd_discovered_targets == 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus target empty
      description: "Prometheus has no target in service discovery\n  VALUE = {{ $value\
        \ }}\n Instance =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheustargetscrapingslow
    title: PrometheusTargetScrapingSlow
    condition: A
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: prometheus_target_interval_length_seconds{quantile="0.9"} / on (interval,
          instance, job) prometheus_target_interval_length_seconds{quantile="0.5"}
          > 1.05
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus target scraping slow
      description: "Prometheus is scraping exporters slowly since it exceeded the\
        \ requested interval time. Your Prometheus server is under-provisioned.\n\
        \  VALUE = {{ $value }}\n Instance =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheuslargescrape
    title: PrometheusLargeScrape
    condition: A
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: increase(prometheus_target_scrapes_exceeded_sample_limit_total[10m])
          > 10
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus large scrape
      description: "Prometheus has many scrapes that exceed the sample limit\n  VALUE\
        \ = {{ $value }}\n Instance =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheustargetscrapeduplicate
    title: PrometheusTargetScrapeDuplicate
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: increase(prometheus_target_scrapes_sample_duplicate_timestamp_total[5m])
          > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus target scrape duplicate
      description: "Prometheus has many samples rejected due to duplicate timestamps\
        \ but different values\n  VALUE = {{ $value }}\n Instance =  {{ $labels.instance\
        \ }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheustsdbcheckpointcreationfailures
    title: PrometheusTsdbCheckpointCreationFailures
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: increase(prometheus_tsdb_checkpoint_creations_failed_total[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus TSDB checkpoint creation failures
      description: "Prometheus encountered {{ $value }} checkpoint creation failures\n\
        \  VALUE = {{ $value }}\n Instance =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheustsdbcheckpointdeletionfailures
    title: PrometheusTsdbCheckpointDeletionFailures
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: increase(prometheus_tsdb_checkpoint_deletions_failed_total[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus TSDB checkpoint deletion failures
      description: "Prometheus encountered {{ $value }} checkpoint deletion failures\n\
        \  VALUE = {{ $value }}\n Instance =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheustsdbcompactionsfailed
    title: PrometheusTsdbCompactionsFailed
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: increase(prometheus_tsdb_compactions_failed_total[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus TSDB compactions failed
      description: "Prometheus encountered {{ $value }} TSDB compactions failures\n\
        \  VALUE = {{ $value }}\n Instance =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheustsdbheadtruncationsfailed
    title: PrometheusTsdbHeadTruncationsFailed
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: increase(prometheus_tsdb_head_truncations_failed_total[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus TSDB head truncations failed
      description: "Prometheus encountered {{ $value }} TSDB head truncation failures\n\
        \  VALUE = {{ $value }}\n Instance =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheustsdbreloadfailures
    title: PrometheusTsdbReloadFailures
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: increase(prometheus_tsdb_reloads_failures_total[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus TSDB reload failures
      description: "Prometheus encountered {{ $value }} TSDB reload failures\n  VALUE\
        \ = {{ $value }}\n Instance =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheustsdbwalcorruptions
    title: PrometheusTsdbWalCorruptions
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: increase(prometheus_tsdb_wal_corruptions_total[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus TSDB WAL corruptions
      description: "Prometheus encountered {{ $value }} TSDB WAL corruptions\n  VALUE\
        \ = {{ $value }}\n Instance =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheustsdbwaltruncationsfailed
    title: PrometheusTsdbWalTruncationsFailed
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: increase(prometheus_tsdb_wal_truncations_failed_total[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus TSDB WAL truncations failed
      description: "Prometheus encountered {{ $value }} TSDB WAL truncation failures\n\
        \  VALUE = {{ $value }}\n Instance =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheusenginequerylogfailure
    title: PrometheusEngineQueryLogFailure
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: increase(prometheus_engine_query_log_failures_total[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus engine query log failure
      description: "Prometheus encountered {{ $value }} engine query log failures\n\
        \  VALUE = {{ $value }}\n Instance =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheusnotificationserror
    title: PrometheusNotificationsError
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: increase(prometheus_notifications_errors_total[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus notifications error
      description: "Prometheus encountered {{ $value }} notifications errors\n  VALUE\
        \ = {{ $value }}\n Pod = {{ $labels.pod }} \n Instance =  {{ $labels.instance\
        \ }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheuslistoperationsfailed
    title: PrometheusListOperationsFailed
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: increase(prometheus_operator_list_operations_failed_total[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus list operations failed
      description: "Prometheus encountered {{ $value }} list operations failures\n\
        \  VALUE = {{ $value }}\n Pod = {{ $labels.pod }} \n Instance =  {{ $labels.instance\
        \ }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheuswatchoperationsfailed
    title: PrometheusWatchOperationsFailed
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: increase(prometheus_operator_watch_operations_failed_total[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus watch operations failed
      description: "Prometheus encountered {{ $value }} watch operations failures\n\
        \  VALUE = {{ $value }}\n Pod = {{ $labels.pod }} \n Instance =  {{ $labels.instance\
        \ }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheusnodesyncfailed
    title: PrometheusNodeSyncFailed
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: increase(prometheus_operator_node_syncs_failed_total[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus node sync failed
      description: "Prometheus encountered {{ $value }} node sync failures\n  VALUE\
        \ = {{ $value }}\n Pod = {{ $labels.pod }} \n Instance =  {{ $labels.instance\
        \ }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheussdhttpfailure
    title: PrometheusSdHttpFailure
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: increase(prometheus_sd_http_failures_total[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus SD HTTP failure counter increased.
      description: "Prometheus encountered {{ $value }} SD HTTP failures\n  VALUE\
        \ = {{ $value }}\n Pod = {{ $labels.pod }} \n Instance =  {{ $labels.instance\
        \ }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: prometheusoperatorreconcileerror
    title: PrometheusOperatorReconcileError
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: increase(prometheus_operator_reconcile_errors_total[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Prometheus operator reconcile error counter increased
      description: "Prometheus operator encountered {{ $value }} reconcile errors\n\
        \  VALUE = {{ $value }}\n Pod = {{ $labels.pod }} \n Instance =  {{ $labels.instance\
        \ }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
