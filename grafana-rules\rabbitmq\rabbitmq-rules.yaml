apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: rabbitmq-folder
spec:
  allowCrossNamespaceImport: true
  title: rabbitmq
  instanceSelector:
    matchLabels:
      dashboards: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: rabbitmq-rules-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: rabbitmq-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana
  interval: 1m
  rules:
  - uid: ewsrabbitmqnodedown
    title: EWSRabbitmqNodeDown
    condition: C
    for: 2m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: rabbitmq_build_info == 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: RabbitMQ cluster node down
      description: Rabbitmq node down (instance {{ $labels.instance }})
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: ewsrabbitmqtoomanyconnections
    title: EWSRabbitmqTooManyConnections
    condition: C
    for: 2m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(rabbitmq_connections) > 500
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Rabbitmq too many connections
      description: RabbitMQ instance has too many connections (> 500)  VALUE = {{
        $value }}  (instance {{ $labels.instance }})
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: ewsrabbitmqnodenotdistributed
    title: EWSRabbitmqNodeNotDistributed
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: erlang_vm_dist_node_state < 3
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Rabbitmq node not distributed
      description: Distribution link state is not 'up'  VALUE = {{ $value }}.  The
        current state of the distribution link.  The state is represented as a numerical
        value where pending=1, up_pending=2 and up=3. (instance {{ $labels.instance
        }})
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: ewsrabbitmqfiledescriptorsusage
    title: EWSRabbitmqFileDescriptorsUsage
    condition: C
    for: 2m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: rabbitmq_process_open_fds / rabbitmq_process_max_fds * 100 > 80
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Rabbitmq file descriptors usage high
      description: A node use more than 80% of file descriptors  VALUE = {{ $value
        }}  (instance {{ $labels.instance }})
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: ewsrabbitmqtoomuchunack
    title: EWSRabbitmqTooMuchUnack
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: rabbitmq_queue_messages_unacked > 20
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Rabbitmq too much unack for last 5 minutes
      description: Too much unacknowledged messages  VALUE = {{ $value }} (instance
        {{ $labels.instance }})
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: ewsrabbitmqtoomanyfailedauthattempts
    title: EWSRabbitmqTooManyFailedAuthAttempts
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(rabbitmq_auth_attempts_failed_total[2m]) > 10
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Rabbitmq too many failed auth attempts for last 2 minutes
      description: Too many failed authentication attempts  VALUE = {{ $value }}  (instance
        {{ $labels.instance }})
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: ewsrabbitmqalarmfreediskspaceinstance
    title: EWSRabbitmqAlarmFreeDiskSpaceInstance
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: rabbitmq_alarms_free_disk_space_watermark == 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Rabbitmq alarm free disk space watermark alarm
      description: Rabbit MQ FreeDisk Space watermark alarm on the instance =  {{
        $labels.instance }})  VALUE = {{ $value }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: ewsrabbitmqalarmmemoryused
    title: EWSRabbitmqAlarmMemoryUsed
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: rabbitmq_alarms_memory_used_watermark == 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Rabbitmq alarm memory used watermark alarm
      description: Rabbit MQ Memory Used watermark alarm on the instance =  {{ $labels.instance
        }})  VALUE = {{ $value }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: rabbitmqmemoryhigh
    title: RabbitmqMemoryHigh
    condition: C
    for: 2m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: rabbitmq_process_resident_memory_bytes / rabbitmq_resident_memory_limit_bytes
          * 100 > 90
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: RabbitMQ memory high
      description: A node use more than 90% of allocated RAM Instance = {{ $labels.instance
        }})  VALUE = {{ $value }}
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: rabbitmqfiledescriptorsusagehigh
    title: RabbitmqFileDescriptorsUsageHigh
    condition: C
    for: 2m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: rabbitmq_process_open_fds / rabbitmq_process_max_fds * 100 > 90
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: RabbitMQ file descriptors usage high
      description: A node use more than 90% of file descriptors Instance = {{ $labels.instance
        }})  VALUE = {{ $value }}
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: rabbitmqunroutablemessages
    title: RabbitmqUnroutableMessages
    condition: C
    for: 30m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(rabbitmq_channel_messages_unroutable_returned_total[1m]) >
          1000 or increase(rabbitmq_channel_messages_unroutable_dropped_total[1m])
          > 1000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: RabbitMQ unroutable messages
      description: RabbitMQ has returned or dropped messages more than the threshold
        of 1000 for the last 30 minutes
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: rabbitmqinstancesdifferentversions
    title: RabbitmqInstancesDifferentVersions
    condition: C
    for: 1h
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: count(count(rabbitmq_build_info) by (rabbitmq_version)) > 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: RabbitMQ instances different versions
      description: Running different version of RabbitMQ in the same cluster, can
        lead to failure  Instance = {{ $labels.instance }})   VALUE = {{ $value }}
        rabbitmq_version = {{ $labels.rabbitmq_version }}
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: ewsrabbitmqtoomuchnotconsumedmessages
    title: EWSRabbitmqTooMuchNotConsumedMessages
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(rabbitmq_queue_messages_ready) > 1000000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
    annotations:
      summary: Rabbitmq too much not consumed messages (instance {{ $labels.instance
        }})
      description: Too much not consumed messages VALUE = {{ $value }} LABELS = {{
        $labels }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
