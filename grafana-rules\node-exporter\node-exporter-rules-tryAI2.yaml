apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: node-exporter-folder
spec:
  allowCrossNamespaceImport: true
  title: node-exporter
  instanceSelector:
    matchLabels:
      dashboards: grafana
---

apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: node-exporter-rules-alert-rules-try
spec:
  allowCrossNamespaceImport: true
  folderRef: node-exporter-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana  
  interval: 1m
  rules:
    - uid: HostHighCpuLoadWarning
      title: HostHighCpuLoadWarning
      condition: A
      data:
        - refId: A
          model:
            expr: >
              (sum by (instance) (avg by (mode, instance)
              (rate(node_cpu_seconds_total{mode!="idle", job!="scylla-db-hosts-sm"}[2m])))
              * 100 > 80) * on (instance) group_left(nodename) node_uname_info
            datasource:
              uid: Mimir
              type: prometheus
            instant: true
            intervalMs: 1000
            legendFormat: "__auto"
            maxDataPoints: 43200
            range: false
      for: 10m
      labels:
        severity: warning
        department: DevOps
        component: Infrastructure
      annotations:
        summary: Host high CPU load warning
        description: >-
          CPU load on node {{ $labels.nodename }} with ip {{ $labels.instance }}
          is high > 80%. CurrentValue = {{ $value }}%
      noDataState: NoData
      execErrState: Error

    - uid: HostHighCpuLoadCritical
      title: HostHighCpuLoadCritical
      condition: A
      data:
        - refId: A
          model:
            expr: >
              (sum by (instance) (avg by (mode, instance)
              (rate(node_cpu_seconds_total{mode!="idle", job!="scylla-db-hosts-sm"}[2m])))
              * 100 > 90) * on (instance) group_left(nodename) node_uname_info
            datasource:
              uid: Mimir
              type: prometheus
            instant: true
            intervalMs: 1000
            legendFormat: "__auto"
            maxDataPoints: 43200
            range: false
      for: 10m
      labels:
        severity: critical
        department: DevOps
        component: Infrastructure
      annotations:
        summary: Host high CPU load is critical
        description: >-
          CPU load on node {{ $labels.nodename }} with ip {{ $labels.instance }}
          is critically high > 90% for 10 minutes. CurrentValue = {{ $value }}%
      noDataState: NoData
      execErrState: Error

    - uid: ScyllaDBHostHighCpuLoadWarning
      title: ScyllaDBHostHighCpuLoadWarning
      condition: A
      data:
        - refId: A
          model:
            expr: >
              (sum by (instance) (avg by (mode, instance)
              (rate(node_cpu_seconds_total{mode!="idle", job="scylla-db-hosts-sm"}[2m])))
              * 100 > 80) * on (instance) group_left(nodename) node_uname_info
            datasource:
              uid: Mimir
              type: prometheus
            instant: true
            intervalMs: 1000
            legendFormat: "__auto"
            maxDataPoints: 43200
            range: false
      for: 15m
      labels:
        severity: warning
        department: DevOps
        component: Infrastructure
      annotations:
        summary: ScyllaDB host high CPU load warning
        description: >-
          ScyllaDB CPU load on node {{ $labels.nodename }} with ip {{
          $labels.instance }} is high > 80%. CurrentValue = {{ $value }}%
      noDataState: NoData
      execErrState: Error

    - uid: ScyllaDBHostHighCpuLoadCritical
      title: ScyllaDBHostHighCpuLoadCritical
      condition: A
      data:
        - refId: A
          model:
            expr: >
              (sum by (instance) (avg by (mode, instance)
              (rate(node_cpu_seconds_total{mode!="idle", job="scylla-db-hosts-sm"}[2m])))
              * 100 > 90) * on (instance) group_left(nodename) node_uname_info
            datasource:
              uid: Mimir
              type: prometheus
            instant: true
            intervalMs: 1000
            legendFormat: "__auto"
            maxDataPoints: 43200
            range: false
      for: 15m
      labels:
        severity: critical
        department: DevOps
        component: Infrastructure
      annotations:
        summary: ScyllaDB host high CPU load is critical
        description: >-
          ScyllaDB CPU load on node {{ $labels.nodename }} with ip {{
          $labels.instance }} is critically high > 90% for 15 minutes. CurrentValue =
          {{ $value }}%
      noDataState: NoData
      execErrState: Error

    - uid: HostCpuHighIowait
      title: HostCpuHighIowait
      condition: A
      data:
        - refId: A
          model:
            expr: >
              (avg by (instance) (rate(node_cpu_seconds_total{mode="iowait"}[5m]))
              * 100 > 50) * on(instance) group_left(nodename) node_uname_info
            datasource:
              uid: Mimir
              type: prometheus
            instant: true
            intervalMs: 1000
            legendFormat: "__auto"
            maxDataPoints: 43200
            range: false
      for: 10m
      labels:
        severity: warning
        department: DevOps
        component: Infrastructure
      annotations:
        summary: Host CPU high iowait
        description: >-
          CPU iowait > 50% on node {{ $labels.nodename }} with ip {{
          $labels.instance }}. A high iowait means that you are disk or network
          bound.
      noDataState: NoData
      execErrState: Error

    - uid: HostMemoryIsUnderutilized
      title: HostMemoryIsUnderutilized
      condition: A
      data:
        - refId: A
          model:
            expr: >
              (100 - (avg_over_time(node_memory_MemAvailable_bytes[30m]) /
              node_memory_MemTotal_bytes * 100) < 20) * on(instance) group_left(nodename)
              node_uname_info
            datasource:
              uid: Mimir
              type: prometheus
            instant: true
            intervalMs: 1000
            legendFormat: "__auto"
            maxDataPoints: 43200
            range: false
      for: 168h
      labels:
        severity: info
        department: DevOps
        component: Infrastructure
      annotations:
        summary: Host Memory is underutilized
        description: >-
          Node {{ $labels.nodename }} with ip {{ $labels.instance }} memory is <
          20% for 1 week. Current usage: {{ $value }}%. Consider reducing memory
          space.
      noDataState: NoData
      execErrState: Error

    # (Continue adding the rest of the converted alerts following the same pattern)
