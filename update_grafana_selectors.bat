@echo off
echo Updating Grafana selectors from "app: grafana" to "dashboards: \"grafana\""...

powershell -Command "(Get-Content 'grafana-rules\vault\vault.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\vault\vault.yaml'"
powershell -Command "(Get-Content 'grafana-rules\ssl-checks\ssl-alarms.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\ssl-checks\ssl-alarms.yaml'"
powershell -Command "(Get-Content 'grafana-rules\sport\websocket-net.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\sport\websocket-net.yaml'"
powershell -Command "(Get-Content 'grafana-rules\sport\tournaments-service.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\sport\tournaments-service.yaml'"
powershell -Command "(Get-Content 'grafana-rules\scylladb\scylladb-recording-latency.rules.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\scylladb\scylladb-recording-latency.rules.yaml'"
powershell -Command "(Get-Content 'grafana-rules\scylladb\scylladb-recording-common.rules.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\scylladb\scylladb-recording-common.rules.yaml'"
powershell -Command "(Get-Content 'grafana-rules\scylladb\scylladb-alerting.rules.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\scylladb\scylladb-alerting.rules.yaml'"
powershell -Command "(Get-Content 'grafana-rules\regulatory\nra-checks.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\regulatory\nra-checks.yaml'"
powershell -Command "(Get-Content 'grafana-rules\redis\redis.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\redis\redis.yaml'"
powershell -Command "(Get-Content 'grafana-rules\rabbitmq\rabbitmq-rules.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\rabbitmq\rabbitmq-rules.yaml'"
powershell -Command "(Get-Content 'grafana-rules\prometheus\prometheus-rules.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\prometheus\prometheus-rules.yaml'"
powershell -Command "(Get-Content 'grafana-rules\postgresql\postgres-exporter.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\postgresql\postgres-exporter.yaml'"
powershell -Command "(Get-Content 'grafana-rules\postgresql\postgres-aggregation-checks.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\postgresql\postgres-aggregation-checks.yaml'"
powershell -Command "(Get-Content 'grafana-rules\node-exporter\node-exporter-rules.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\node-exporter\node-exporter-rules.yaml'"
powershell -Command "(Get-Content 'grafana-rules\nginx\nginx-plus.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\nginx\nginx-plus.yaml'"
powershell -Command "(Get-Content 'grafana-rules\kubernetes\kubernetes-rules.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\kubernetes\kubernetes-rules.yaml'"
powershell -Command "(Get-Content 'grafana-rules\kafka\kafka.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\kafka\kafka.yaml'"
powershell -Command "(Get-Content 'grafana-rules\jenkins\jenkins-rules.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\jenkins\jenkins-rules.yaml'"
powershell -Command "(Get-Content 'grafana-rules\fluentbit\fluentbit.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\fluentbit\fluentbit.yaml'"
powershell -Command "(Get-Content 'grafana-rules\elastic-stack\logstash-rules.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\elastic-stack\logstash-rules.yaml'"
powershell -Command "(Get-Content 'grafana-rules\elastic-stack\elasticsearch.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\elastic-stack\elasticsearch.yaml'"
powershell -Command "(Get-Content 'grafana-rules\dataguard\dataguard.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\dataguard\dataguard.yaml'"
powershell -Command "(Get-Content 'grafana-rules\crm\outbound-proxy-rules.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\crm\outbound-proxy-rules.yaml'"
powershell -Command "(Get-Content 'grafana-rules\coredns\coredns-rules.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\coredns\coredns-rules.yaml'"
powershell -Command "(Get-Content 'grafana-rules\clickhouse\clickhouse-rules.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\clickhouse\clickhouse-rules.yaml'"
powershell -Command "(Get-Content 'grafana-rules\clickhouse\clickhouse-delay-exporter.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\clickhouse\clickhouse-delay-exporter.yaml'"
powershell -Command "(Get-Content 'grafana-rules\cloudflare\cloudflare-rules.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\cloudflare\cloudflare-rules.yaml'"
powershell -Command "(Get-Content 'grafana-rules\barman\barman.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\barman\barman.yaml'"
powershell -Command "(Get-Content 'grafana-rules\artifactory\artifactory-rules.yaml') -replace '      app: grafana', '      dashboards: \"grafana\"' | Set-Content 'grafana-rules\artifactory\artifactory-rules.yaml'"

echo Done! All Grafana selectors have been updated.
pause
