  - uid: hosthighcpuloadwarning
    title: HostHighCpuLoadWarning
    condition: A
    for: 10m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        datasource:
          type: prometheus
          uid: Mimir
        editorMode: code
        expr: (sum by (instance) (avg by (mode, instance) (rate(node_cpu_seconds_total{mode!="idle",
          job!="scylla-db-hosts-sm"}[2m]))) * 100 > 80) * on (instance) group_left
          (nodename) node_uname_info
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Host high CPU load warning
      description: "CPU load on node {{ $labels.nodename }} with ip {{ $labels.instance\
        \ }} is high > 80%\n  CurrentValue = {{ $value }}%"
    noDataState: NoData
    execErrState: Error
    isPaused: false
