apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: cloudflare-folder
spec:
  allowCrossNamespaceImport: true
  title: cloudflare
  instanceSelector:
    matchLabels:
      dashboards: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: cloudflare-rules-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: cloudflare-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana
  interval: 1m
  rules:
  - uid: cloudflareworkererrors
    title: CloudflareWorkerErrors
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(cloudflare_worker_errors_count[5m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Cloudflare worker raised new errors recently
      description: "Cloudflare worker errors\n  VALUE = {{ $value }}\n instance =\
        \ {{ $labels.instance }})"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: cloudflarezonepoolhealthstatus
    title: CloudflareZonePoolHealthStatus
    condition: C
    for: 2m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: cloudflare_zone_pool_health_status == 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Load Balancing Pool Unhealthy
      description: "Cloudflare zone pool health status\n  VALUE = {{ $value }}\n ZONE\
        \ = {{ $labels.zone }}\n instance =  {{ $labels.instance }})"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: cloudflareincreasedthreatdetection
    title: CloudflareIncreasedThreatDetection
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: irate(cloudflare_zone_threats_total[5m]) > 100
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Elevated Number of Threats Detected
      description: More than 100 threats have been detected in the past 5 minutes
        for zone {{ $labels.zone }}
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: cloudflarehttp5xxerrorrate
    title: CloudflareHttp5xxErrorRate
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum by(zone) (rate(cloudflare_zone_requests_status{status=~"5.."}[5m]))
          / sum by(zone) (rate(cloudflare_zone_requests_status[5m])) * 100 > 5
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Cloudflare http 5xx error rate
      description: "Cloudflare high HTTP 5xx error rate (> 5% for domain {{ $labels.zone\
        \ }})\n  VALUE = {{ $value }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: cloudflarebandwidthspike
    title: CloudflareBandwidthSpike
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: rate(cloudflare_zone_bandwidth_total[5m]) > 1e9
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Bandwidth Spike Detected
      description: "Bandwidth usage has exceeded 1 Gbps over the past 5 minutes. \n\
        \ VALUE = {{ $value }} \n ZONE = {{ $labels.zone }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: cloudflarelogpushjobfailures
    title: CloudflareLogpushJobFailures
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: rate(cloudflare_logpush_failed_jobs_account_count[5m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Failed Logpush Jobs Detected
      description: "There are failed Logpush jobs at the account level.\n VALUE =\
        \ {{ $value }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: elevatedworkercputime
    title: ElevatedWorkerCPUTime
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: rate(cloudflare_worker_cpu_time[5m]) > 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: High Worker CPU Time
      description: "A Cloudflare Worker is consuming more than 1 second of CPU time\
        \ over the past 5 minutes.\n VALUE = {{ $value }}\n"
    noDataState: NoData
    execErrState: Error
    isPaused: false
