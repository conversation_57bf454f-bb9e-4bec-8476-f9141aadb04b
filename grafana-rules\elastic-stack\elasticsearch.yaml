apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: elastic-stack-folder
spec:
  allowCrossNamespaceImport: true
  title: elastic-stack
  instanceSelector:
    matchLabels:
      dashboards: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: elasticsearch-rules-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: elastic-stack-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana
  interval: 1m
  rules:
  - uid: elasticsearchclusterhealth
    title: ElasticsearchClusterHealth
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: elasticsearch_cluster_health_up == 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: elasticsearch
      department: DevOps
    annotations:
      summary: 'Elasticsearch is down: Cluster Health UP is not ok'
      description: Elasticsearch is down for prod cluster. Cluster Health UP metric
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: elasticsearchdown
    title: ElasticsearchDown
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: elasticsearch_clusterinfo_up != 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: elasticsearch
      department: DevOps
    annotations:
      summary: Elasticsearch is down for prod cluster
      description: Elasticsearch is down for prod cluster. ClusterInfo UP metric
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: elasticsearchdiskusagehighwarning
    title: ElasticsearchDiskUsageHighWarning
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: (1-(elasticsearch_filesystem_data_available_bytes/elasticsearch_filesystem_data_size_bytes))
          * 100 > 80
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: elasticsearch
      department: DevOps
    annotations:
      summary: Elasticsearch disk space nearing low disk watermark
      description: Elasticsearch disk running out of space on {{ $labels.host }}.
        Usage is equal or above 80% - {{ $value }}% used.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: elasticsearchdiskusagehighcritical
    title: ElasticsearchDiskUsageHighCritical
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: (1-(elasticsearch_filesystem_data_available_bytes/elasticsearch_filesystem_data_size_bytes))
          * 100 > 84
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: elasticsearch
      department: DevOps
    annotations:
      summary: Elasticsearch disk space reaching low disk watermark
      description: Elasticsearch disk free space is getting low on {{ $labels.host
        }}. Usage is equal or above 84% - {{ $value }}% used.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: elasticsearchcpuusagehigh
    title: ElasticsearchCPUUsageHigh
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum (elasticsearch_process_cpu_percent) / count (elasticsearch_process_cpu_percent)
          > 85
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: elasticsearch
      department: DevOps
    annotations:
      summary: Elasticsearch prod cluster high CPU usage above 85%
      description: Elasticsearch prod cluster high CPU usage above 85% - {{ $value
        }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: elasticsearchjvmusagehigh
    title: ElasticsearchJVMUsageHigh
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: (sum (elasticsearch_jvm_memory_used_bytes{area="heap"} / elasticsearch_jvm_memory_max_bytes{area="heap"}
          ) / sum( elasticsearch_cluster_health_number_of_nodes)) * 100 > 85
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: elasticsearch
      department: DevOps
    annotations:
      summary: Elasticsearch prod cluster high JVM usage above 85%
      description: Elasticsearch prod cluster, high JVM usage above 85% - {{ $value
        }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: elasticsearchpendingtasks
    title: ElasticsearchPendingTasks
    condition: C
    for: 15m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: elasticsearch_cluster_health_number_of_pending_tasks > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: elasticsearch
      department: DevOps
    annotations:
      summary: Elasticsearch has pending tasks on prod cluster
      description: Elasticsearch has pending tasks on prod cluster - {{ $value }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: elasticsearchunassignedshards
    title: ElasticsearchUnassignedShards
    condition: C
    for: 15m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: elasticsearch_cluster_health_unassigned_shards > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: elasticsearch
      department: DevOps
    annotations:
      summary: Elasticsearch has unassigned shards on prod cluster
      description: Elasticsearch has unassigned shards on prod cluster - {{ $value
        }}
      value: '{{ $value }}'
      runbook_url: https://confluence.egt-digital.com/display/K8S/Unassigned+Elasticsearch+shards+of+an+index
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: elasticsearchclusterred
    title: ElasticsearchClusterRed
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: elasticsearch_cluster_health_status{color="red"} == 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: elasticsearch
      department: DevOps
    annotations:
      summary: Elasticsearch Cluster Red
      description: '"Elastic Cluster Red status\n  VALUE = {{ $value }}"

        (instance {{ $labels.instance }})'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: elasticsearchclusteryellow
    title: ElasticsearchClusterYellow
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: elasticsearch_cluster_health_status{color="yellow"} == 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: elasticsearch
      department: DevOps
    annotations:
      summary: Elasticsearch Cluster Yellow
      description: '"Elastic Cluster Yellow status\n  VALUE = {{ $value }}"

        (instance {{ $labels.instance }})'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: elasticsearchnonewdocuments
    title: ElasticsearchNoNewDocuments
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(elasticsearch_indices_docs{es_data_node="true"}[10m]) < 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: elasticsearch
      department: DevOps
    annotations:
      summary: Elasticsearch no new documents
      description: '"No new documents for 10 min!\n  VALUE = {{ $value }}"

        (instance {{ $labels.instance }})'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: elasticsearchoscpuhigh
    title: ElasticsearchOsCPUHigh
    condition: C
    for: 10m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: avg_over_time(elasticsearch_os_cpu_percent[5m]) > 90
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: elasticsearch
      department: DevOps
    annotations:
      summary: Elasticsearch OS CPU usage is high for the last 5 mins
      description: "Elasticsearch OS CPU usage is high\n  VALUE = {{ $value }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: elasticsearchprocesscpuhigh
    title: ElasticsearchProcessCPUHigh
    condition: C
    for: 10m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: avg_over_time(elasticsearch_process_cpu_percent[5m]) > 90
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: elasticsearch
      department: DevOps
    annotations:
      summary: Elasticsearch Process CPU usage is high for the last 5 mins
      description: "Elasticsearch Process CPU usage is high\n  VALUE = {{ $value }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: elasticsearchindexpatternsizewarning
    title: ElasticsearchIndexPatternSizeWarning
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum by(pattern) (label_replace(elasticsearch_indices_store_size_bytes_total,
          "pattern", "$1", "index", "(.+)-\\d{4}-\\d{2}-\\d{2}")) > 35184372088832
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
    annotations:
      summary: Index pattern size warning (>32 TiB)
      description: Index pattern {{ $labels.pattern }} has reached {{ $value }} bytes.
    noDataState: NoData
    execErrState: Error
    isPaused: false
