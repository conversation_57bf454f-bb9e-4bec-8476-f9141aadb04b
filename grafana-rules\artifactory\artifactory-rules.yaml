apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: artifactory-folder
spec:
  allowCrossNamespaceImport: true
  title: artifactory
  instanceSelector:
    matchLabels:
      dashboards: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: artifactory-rules-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: artifactory-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana
  interval: 1m
  rules:
  - uid: artifactorydown
    title: ArtifactoryDown
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: artifactory_up == 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    # - refId: B
    #   datasourceUid: __expr__
    #   model:
    #     type: reduce
    #     reducer:
    #       type: last
    #     expression: A
    #     intervalMs: 1000
    #     maxDataPoints: 43200
    #     refId: B
    # - refId: C
    #   datasourceUid: __expr__
    #   model:
    #     type: threshold
    #     conditions:
    #     - evaluator:
    #         type: gt
    #         params:
    #         - 0
    #       operator:
    #         type: and
    #       query:
    #         params:
    #         - C
    #     intervalMs: 1000
    #     maxDataPoints: 43200
    #     refId: C

    labels:
      department: DevOps
      severity: critical
      component: artifactory
    annotations:
      summary: Artifactory is down
      description: Artifactory is down
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: artifactorydiskusagehigh
    title: ArtifactoryDiskUsageHigh
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: (1-(artifactory_storage_filestore_free_bytes/artifactory_storage_filestore_bytes))
          * 100 > 90
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      department: DevOps
      severity: critical
      component: artifactory
    annotations:
      summary: Artifactory disk running out of space {{ $labels.instance }} -  {{
        $value }} used
      description: Artifactory disk running out of space {{ $labels.instance }} -
        {{ $value }} used
      runbook_url: https://confluence.egt-digital.com/display/K8S/Disk+space+alarms
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: artifactorylicenseexpiringsoon
    title: ArtifactoryLicenseExpiringSoon
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: artifactory_system_license < (60 * 60 * 24 * 30)
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      department: DevOps
      severity: warning
      component: artifactory
    annotations:
      summary: Artifactory license expiring soon
      description: 'Artifactory license will expire in less than 30 days.

        Remaining days: `{{ with query "artifactory_system_license / 84600" }}{{ .
        | first | value | printf "%.2f" }}{{ end }}`'
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: artifactorylicenseexpiringin2days
    title: ArtifactoryLicenseExpiringIn2Days
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: artifactory_system_license < (60 * 60 * 24 * 2)
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      department: DevOps
      severity: critical
      component: artifactory
    annotations:
      summary: Artifactory license expiring soon
      description: 'Artifactory license will expire in less than 2 days.

        Remaining days: `{{ with query "artifactory_system_license / 84600" }}{{ .
        | first | value | printf "%.2f" }}{{ end }}`'
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: artifactoryapierrors
    title: ArtifactoryAPIErrors
    condition: A
    for: 15m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: increase(artifactory_exporter_total_api_errors[5m]) > 10
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      department: DevOps
      severity: critical
      component: artifactory
    annotations:
      summary: High number of API errors in Artifactory
      description: Artifactory API error count has exceeded 10 errors in the last
        5 minutes. Check the logs for potential issues.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: artifactorysystemhealth
    title: ArtifactorySystemHealth
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: artifactory_system_healthy != 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      department: DevOps
      severity: critical
      component: artifactory
    annotations:
      summary: Artifactory system health is not OK
      description: Artifactory system health is not OK. Check the logs for potential
        issues.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
