apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: jenkins-folder
spec:
  allowCrossNamespaceImport: true
  title: jenkins
  instanceSelector:
    matchLabels:
      dashboards: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: jenkins-rules-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: jenkins-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana
  interval: 1m
  rules:
  - uid: jenkinsdown
    title: JenkinsDown
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: default_jenkins_up != 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Jenkins down
      description: "Jenkins down: Instance : `{{$labels.instance}}`.\n  VALUE = {{\
        \ $value }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: jenkinsoffline
    title: JenkinsOffline
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: jenkins_node_offline_value > 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Jenkins offline
      description: "Jenkins offline: Instance : `{{$labels.instance}}`.\n  VALUE =\
        \ {{ $value }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: jenkinshealthcheck
    title: JenkinsHealthcheck
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: jenkins_health_check_score < 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Jenkins healthcheck failure
      description: "Jenkins healthcheck score: {{$value}}. Healthcheck failure for\
        \ Instance `{{$labels.instance}}`.\n  VALUE = {{ $value }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: jenkinsoutdatedplugins
    title: JenkinsOutdatedPlugins
    condition: A
    for: 24h
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: sum(jenkins_plugins_withUpdate) by (instance) > 3
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Jenkins outdated plugins
      description: "Plugins need update\n Instance {{ $labels.instance }}\n  VALUE\
        \ = {{ $value }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: jenkinsbuildshealthscore
    title: JenkinsBuildsHealthScore
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: default_jenkins_builds_health_score{jenkins_job=~".*/(develop|master|release)$"}
          < 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Jenkins builds health score failure
      description: "Healthcheck failure for job {{ $labels.jenkins_job }} on Instance\
        \ = {{$labels.instance}}.\n VALUE = {{ $value }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: jenkinslowdiskspace
    title: JenkinsLowDiskSpace
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: default_jenkins_file_store_available_bytes / default_jenkins_file_store_capacity_bytes
          < 0.1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Jenkins low disk space. Less than 10% space left.
      description: "Low disk space for Instance = {{$labels.instance}}.\n VALUE =\
        \ {{ $value }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: jenkinsrunfailuretotal
    title: JenkinsRunFailureTotal
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: delta(jenkins_runs_failure_total[1h]) > 100
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Jenkins run failure total
      description: "Job run failures: ({{$value}}) {{$labels.jenkins_job}}. Healthcheck\
        \ failure for instance `{{$labels.instance}}` \n  VALUE = {{ $value }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: jenkinsbuildtestsfailing
    title: JenkinsBuildTestsFailing
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: default_jenkins_builds_last_build_tests_failing > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Jenkins build tests failing
      description: "Last build tests failed: {{$labels.jenkins_job}}. Failed build\
        \ Tests for job `{{$labels.jenkins_job}}` on {{$labels.instance}}\n  VALUE\
        \ = {{ $value }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: jenkinslastbuilddurationhigh
    title: JenkinsLastBuildDurationHigh
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: default_jenkins_builds_last_build_duration_milliseconds > default_jenkins_builds_duration_milliseconds_summary
          * 2
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Jenkins last build tooks abmnormally long compare to average build
        duration.
      description: "Last build duration high. Build take 2x time compared to average.\
        \ High build duration for job `{{$labels.jenkins_job}}` on Instance =  {{$labels.instance}}\n\
        \  VALUE = {{ $value }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: jenkistoomanyfailedbuilds
    title: JenkisTooManyFailedBuilds
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: increase(default_jenkins_builds_failed_build_count_total[5m]) > 5
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Jenkins too many failed builds.
      description: "Too many failed builds, 5 failed for last 5 minutes.\n Job `{{$labels.jenkins_job}}`\n\
        \ n Instance =  {{$labels.instance}}\n  VALUE = {{ $value }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: jenkinspluginfailure
    title: JenkinsPluginFailure
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: jenkins_plugins_failed !=0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Jenkins plugin failure
      description: "Plugin failure. Plugin =  {{$labels.jenkins_plugin}}.\n Instance\
        \ =  {{$labels.instance}}\n  VALUE = {{ $value }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
