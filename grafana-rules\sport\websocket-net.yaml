apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: sport-folder
spec:
  allowCrossNamespaceImport: true
  title: sport
  instanceSelector:
    matchLabels:
      dashboards: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: websocket-net-rules-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: sport-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana
  interval: 1m
  rules:
  - uid: websocketapicpuloadwarning
    title: WebsocketApiCpuLoadWarning
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate{pod=~"ews-websocket-api-net.*"})
          by (pod,node) > 1.5
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: Sport
      component: Service
    annotations:
      summary: Websocket API High CPU Load
      description: Websocket API CPU load exceed 1.5 cores for the last 5 minutes
        on {{ $labels.pod }} located on node {{ $labels.node }}. Currently using {{
        $value }} cores
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: websocketbigprocessinglag
    title: WebsocketBigProcessingLag
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: processing_lag{job=~"ews-websocket-api-net-2-0.*"} > 100 or user_messages_processing_lag{job=~"ews-websocket-api-net-2-0.*"}
          > 100 or user_player_bindings_processing_lag{job=~"ews-websocket-api-net-2-0.*"}
          > 100
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: Sport
      component: Service
    annotations:
      summary: Websockets processing lag warning
      description: 'Websockets: Too big processing lag for {{ $labels.job }} on instance
        {{ $labels.pod }}. Current value: {{ $value }}'
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: websocketfailedexecutions
    title: WebsocketFailedExecutions
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(execution_failed_total{job=~".*websocket.*"}[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: Sport
      component: Service
    annotations:
      summary: 'Websockets: Failed executions count'
      description: 'Websockets: Too many failed executions from pod {{ $labels.pod
        }} regarding controller {{ $labels.controller }} and action {{ $labels.action
        }}'
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: websocketerrordetection
    title: WebsocketErrorDetection
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: count_errors_total{job=~"ews-websocket-api-net-2-0.*"} > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: Sport
      component: Service
    annotations:
      summary: 'Websockets: Error count detection'
      description: 'Websockets: Errors detected from {{ $labels.pod }} for source
        {{ $labels.source }} and type {{ $labels.type }}'
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: websocketbigmessagedeliverydelay
    title: WebsocketBigMessageDeliveryDelay
    condition: C
    for: 30m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: irate(summary_rabbit_messages_processing_delay_sum{job=~"ews-websocket-api-net-2-0.*"}[2m])
          / irate(summary_rabbit_messages_processing_delay_count{job=~"ews-websocket-api-net-2-0.*"}[2m])
          > 100000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: Sport
      component: Service
    annotations:
      summary: 'Websockets: Too big delay in message delivery'
      description: 'Websockets: Message delivery delay exceed 100 000 entries from
        instance {{ $labels.pod }} (type: {{ $labels.type }} and step: {{ $labels.step
        }}). Current value of: {{ $value }}'
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
