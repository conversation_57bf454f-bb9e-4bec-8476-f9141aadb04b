apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: postgresql-folder
spec:
  allowCrossNamespaceImport: true
  title: postgresql
  instanceSelector:
    matchLabels:
      dashboards: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: postgres-exporter-prod-cluster-rules-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: postgresql-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana
  interval: 1m
  rules:
  - uid: postgresqldown
    title: PostgresqlDown
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: pg_up == 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: Database
    annotations:
      summary: Postgresql down
      description: Postgresql instance {{ $labels.instance }} is down
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: postgresqlrestarted
    title: PostgresqlRestarted
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: time() - pg_postmaster_start_time_seconds < 60
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: Database
    annotations:
      summary: Postgresql restarted
      description: Postgresql instance {{ $labels.instance }}) restarted
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: postgresqlexportererror
    title: PostgresqlExporterError
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: pg_exporter_last_scrape_error > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: Database
    annotations:
      summary: Postgresql exporter error
      description: "Postgresql exporter is showing errors on instance {{ $labels.instance\
        \ }}.\n A query may be buggy in query.yaml"
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: postgresqlreplicationlagdb2
    title: PostgresqlReplicationLagDB2
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: pg_replication_lag{instance="************:9187"} > 3
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: Database
    annotations:
      summary: Postgresql replication lag
      description: PostgreSQL DB2 replication lag is going up (> 3s) on instance {{
        $labels.instance }})
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: postgresqlreplicationlagdwh
    title: PostgresqlReplicationLagDWH
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: pg_replication_lag{instance="************:9187"} > 900
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: Database
    annotations:
      summary: Postgresql replication lag
      description: PostgreSQL DWH replication lag is going up (> 15min) on instance
        {{ $labels.instance }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: postgresqlreplicationlagkas
    title: PostgresqlReplicationLagKAS
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: pg_replication_lag{instance="************:9187"} > 1800
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: Database
    annotations:
      summary: Postgresql replication lag
      description: PostgreSQL KAS replication lag is going up (> 30min) on instance
        {{ $labels.instance }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: postgresqltoomanyconnections
    title: PostgresqlTooManyConnections
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum by (namespace, instance) (pg_stat_activity_count) > sum by (namespace,
          instance) ((pg_settings_max_connections) * 0.8)
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DBA
      component: Database
    annotations:
      summary: Postgresql too many connections
      description: "PostgreSQL instance {{ $labels.instance }}) has too many connections\
        \ (> 80%).\n Check in Prometheus using [sum by (datname, instance) (pg_stat_activity_count)]"
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: postgresqldeadlocks
    title: PostgresqlDeadLocks
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(pg_stat_database_deadlocks{datname!~"template.*|postgres"}[1m])
          > 5
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DBA
      component: Database
    annotations:
      summary: Postgresql dead locks
      description: PostgreSQL has dead-locks for datname {{ $labels.datname }} with
        datid {{ $labels.datid }} on instance {{ $labels.instance }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: postgresqlslowqueries
    title: PostgresqlSlowQueries
    condition: C
    for: 2m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: pg_slow_queries > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DBA
      component: Database
    annotations:
      summary: Postgresql slow queries
      description: PostgreSQL executes slow queries on instance {{ $labels.instance
        }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: postgresqltoomanylocksacquired
    title: PostgresqlTooManyLocksAcquired
    condition: C
    for: 2m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum by (service, server, instance) (pg_locks_count) / (sum by (service,
          server, instance) (pg_settings_max_locks_per_transaction * pg_settings_max_connections))
          > 0.2
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: Database
    annotations:
      summary: Postgresql too many locks acquired
      description: Too many locks acquired on the database for server running as {{
        $labels.server }} on the instance {{ $labels.instance }}. If this alert happens
        frequently, we may need to increase the postgres setting max_locks_per_transaction.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: postgreshighoffsetbetweenbackups
    title: PostgresHighOffsetBetweenBackups
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: (sum by (server, instance) (barman_last_backup) - sum by (server, instance)
          (barman_first_backup)) > 60 * 60 * 24 * 2
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: Database
    annotations:
      summary: Postgres backup time gap
      description: 'Server {{ $labels.server }} on instance {{ $labels.instance }}
        has missed a backup: backup time gap larger than 2 days'
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: postgresqlreplicationlagwal-lsn-diff
    title: PostgresqlReplicationLagWAL_LSN_DIFF
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: pg_stat_replication_pg_wal_lsn_diff{client_addr != "************"} >
          5000000000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: Database
    annotations:
      summary: PostgreSQL replication lag is going up
      description: 'PostgreSQL replication lag is going up (> 300s): {{ $value }}
        on instance {{ $labels.instance }}'
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: inactivepostgresreplicationslots
    title: InactivePostgresReplicationSlots
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: pg_inactive_replication_slots_count > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: Database
    annotations:
      summary: Inactive postgres replication slots
      description: Inactive replication slots on instance {{ $labels.instance }}.
        Please call the on-call DBA to review the inactive replication slot and why
        it is down
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: postgrestoomanydeadtuples
    title: PostgresTooManyDeadTuples
    condition: C
    for: 30m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: ((pg_stat_user_tables_n_dead_tup > 1000) / (pg_stat_user_tables_n_live_tup
          + pg_stat_user_tables_n_dead_tup)) >= 0.1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      department: DBA
      component: Database
    annotations:
      summary: PostgreSQL dead tuples is too large
      description: 'The dead tuple ratio of {{$labels.relname}} on database {{$labels.datname}}
        is greater than 5% in cluster {{$labels.cluster_name}}. Instance:  {{ $labels.instance
        }} '
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: postgresnotenoughconnections
    title: PostgresNotEnoughConnections
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum by (datname) (pg_stat_activity_count{datname!~"template.*|postgres"})
          < 5
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DBA
      component: Database
    annotations:
      summary: Postgresql not enough connections
      description: PostgreSQL instance {{ $labels.instance }} should have more connections
        (> 5) for a datname {{ $labels.datname }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: postgresqlremotedb2diskusagetoohigh
    title: PostgresqlRemoteDb2DiskUsageTooHigh
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: (node_filesystem_avail_bytes{job =~ "postgres-sm", mountpoint = "/",
          instance = "*************:9100"}) / 1024^4 < 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: Database
      alert_type: db-disk-alerts
    annotations:
      summary: Postgresql Remote DB 2 disk Usage Too High
      description: Less than 1TB free disk space left on pg instance. Current VALUE
        = {{ printf "%.2f" ($value) }} TB
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: postgrenodeloadcritical
    title: PostgreNodeLoadCritical
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: node_load15{ instance="************:9100"} > 30
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: Database
    annotations:
      summary: Postgres MASTER is using above 30 CPU Node Load
      description: Instance {{ $labels.instance }} is using more than 30 CPU Node
        Load. Current value is {{ $value }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: postgrenodeloadwarning
    title: PostgreNodeLoadWarning
    condition: C
    for: 0m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: node_load15{ instance="************:9100"} > 25
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DBA
      component: Database
    annotations:
      summary: Postgres MASTER is using above 25 CPU Node Load
      description: Instance {{ $labels.instance }} is using more than 25 CPU Node
        Load. Current value is {{ $value }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: postgresqlsplitbrain
    title: PostgresqlSplitBrain
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: count(pg_replication_is_replica{instance!="************:9187", instance!="*************:9187",
          instance!="**************:9187", instance!="**************:9187"} == 0)
          != 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: Database
    annotations:
      summary: Postgresql split brain
      description: Detected Postgres split brain for instance {{ $labels.instance
        }}.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
