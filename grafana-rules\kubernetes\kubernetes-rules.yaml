apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: kubernetes-folder
spec:
  allowCrossNamespaceImport: true
  title: kubernetes
  instanceSelector:
    matchLabels:
      dashboards: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: kubernetes-rules-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: kubernetes-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana
  interval: 1m
  rules:
  - uid: kubernetesnodenotready
    title: KubernetesNodeNotReady
    condition: A
    for: 10m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: kube_node_status_condition{condition="Ready",status="true"} == 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Kubernetes Node not ready
      description: "Node {{ $labels.node }} has been unready for more than 10m \n\
        \  status = {{ $value }}\n  instance = {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kubernetesmasternodedown
    title: KubernetesMasterNodeDown
    condition: A
    for: 2m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: 'count(kube_node_status_condition{condition="Ready", status="true"}
          == 0)

          unless on(node)

          kube_node_role{role="master"}

          '
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Master Node Down in Kubernetes
      description: The master node(s) {{ $labels.node }} is down or not in 'Ready'
        state for more than 2 minutes.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kubernetesnodeconfigerror
    title: KubernetesNodeConfigError
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: kubelet_node_config_error > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Kubernetes Node config error
      description: Node {{ $labels.node }} has a configuration error.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kubernetesnodememorypressure
    title: KubernetesNodeMemoryPressure
    condition: A
    for: 2m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: kube_node_status_condition{condition="MemoryPressure",status="true"}
          > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Kubernetes Node memory pressure
      description: Node {{ $labels.node }} has MemoryPressure condition
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kubernetesnodenetworkunavailable
    title: KubernetesNodeNetworkUnavailable
    condition: A
    for: 2m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: kube_node_status_condition{condition="NetworkUnavailable",status="true"}
          > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Kubernetes Node network unavailable
      description: Node {{ $labels.node }} has NetworkUnavailable condition
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kubernetesnodeoutofpodcapacity
    title: KubernetesNodeOutOfPodCapacity
    condition: A
    for: 2m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: "sum by (node) (\n  kube_pod_status_phase{phase=\"Running\"}\n  * on(pod,\
          \ namespace) group_left(node)\n  kube_pod_info\n)\n/\nsum by (node) ( kube_node_status_allocatable{resource=\"\
          pods\"} )\n* 100 > 90"
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Kubernetes Node out of pod capacity
      description: Node {{ $labels.node }} is out of pod capacity. Currenlty using
        {{ $value }}% of pods capacity.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kubernetesapiservererrors
    title: KubernetesApiServerErrors
    condition: A
    for: 2m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: sum(rate(apiserver_request_total{job="apiserver",code=~"(?:5..)"}[1m]))
          by (instance, job) / sum(rate(apiserver_request_total{job="apiserver"}[1m]))
          by (instance, job) * 100 > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Kubernetes API server errors
      description: Kubernetes API server is experiencing high error rate on API server
        {{ $labels.instance }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kubernetesapiclienterrors
    title: KubernetesApiClientErrors
    condition: A
    for: 2m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: (sum(rate(rest_client_requests_total{code=~"(5).."}[1m])) by (instance,
          job) / sum(rate(rest_client_requests_total[1m])) by (instance, job)) * 100
          > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Kubernetes API client errors
      description: Kubernetes API client is experiencing high error rate
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kubernetesapiserverlatency
    title: KubernetesApiServerLatency
    condition: A
    for: 10m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: histogram_quantile(0.99, sum(rate(apiserver_request_duration_seconds_bucket{verb!~"(?:CONNECT|WATCHLIST|WATCH|PROXY)"}
          [10m])) WITHOUT (subresource)) > 60
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Kubernetes API server latency
      description: Kubernetes API server has a 99th percentile latency of {{ $value
        }} seconds for {{ $labels.verb }} {{ $labels.resource }} in instance {{ $labels.instance
        }}
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kubeletruntimeoperationserrorratehigh
    title: KubeletRuntimeOperationsErrorRateHigh
    condition: A
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: rate(kubelet_runtime_operations_errors_total[5m]) / rate(kubelet_runtime_operations_total[5m])
          > 0.1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Kubelet runtime operations error rate high
      description: 'Kubelet runtime operations error rate is above 10%. Failed operations
        type: {{ $labels.operation_type }} on node {{ $labels.node }} for the last
        5 minutes.'
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kubeproxy-iptables-restore-fail
    title: KubeProxySyncProxyRulesIptablesRestoreFailures
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: increase(kubeproxy_sync_proxy_rules_iptables_restore_failures_total[2m])
          > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: KubeProxy sync proxy rules iptables restore failure
      description: KubeProxy is experienced failures in restoring iptables rules.
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: k8s-pod-container-restart-high
    title: KubernetesPodContainerRestartRateTooHigh
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: floor(increase(kube_pod_container_status_restarts_total[1h])) > 12
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Pod container restart rate is too high
      description: Container {{ $labels.container }} in pod {{ $labels.pod }} restarting
        constantly in last hour for a service {{ $labels.namespace }}/{{ $labels.service
        }} on {{ $labels.instance }}. Total {{ $value }} restarts in last hour.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kubernetespodcrashlooping
    title: KubernetesPodCrashLooping
    condition: A
    for: 10m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: floor(rate(kube_pod_container_status_restarts_total[10m]) * 60 * 10)
          > 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Pod CrashLooping rate too high
      description: Container {{ $labels.container }} in pod {{ $labels.namespace }}/{{
        $labels.pod }} is restarting {{ printf "%.2f" $value }} times / 10 minutes.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: k8s-pod-last-terminated-reason
    title: KubernetesPodContainerStatusLastTerminatedReason
    condition: A
    for: 15m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: sum by (pod, container, reason) (kube_pod_container_status_last_terminated_reason)
          * on (pod,container) group_left sum by (pod, container) (changes(kube_pod_container_status_restarts_total[15m]))
          > 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Pod kube pod container status last terminated reason
      description: 'Pod kube pod container status last terminated reason 15m {{ $labels.namespace
        }}/{{ $labels.pod }} on {{ $labels.node }} with reason: {{ $labels.reason
        }}'
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kubernetespodoomkilled
    title: KubernetesPodOOMKilled
    condition: A
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: kube_pod_container_status_last_terminated_reason{reason="OOMKilled"}
          and on(namespace, pod, container) (increase(kube_pod_container_status_restarts_total[10m])
          > 0)
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Pod was terminated due to Out Of Memory signal
      description: Pod {{ $labels.pod }} was terminated due to Out Of Memory at least
        once in the last 10 minutes.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kubernetespoddisruptionbudgetnothealthy
    title: KubernetesPodDisruptionBudgetNotHealthy
    condition: A
    for: 2m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: kube_poddisruptionbudget_status_current_healthy < 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Kubernetes PodDisruptionBudget not healthy
      description: PodDisruptionBudget {{ $labels.namespace }}/{{ $labels.poddisruptionbudget
        }} is not healthy.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kubernetesjobfailed
    title: KubernetesJobFailed
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: kube_job_status_failed > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Kubernetes Job failed
      description: Job {{ $labels.namespace }}/{{ $labels.job_name }} failed to complete
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kubernetescronjobsuspended
    title: KubernetesCronjobSuspended
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: kube_cronjob_spec_suspend != 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Kubernetes CronJob suspended
      description: CronJob {{ $labels.namespace }}/{{ $labels.cronjob }} is suspended
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kubernetescronjobtoolong
    title: KubernetesCronjobTooLong
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: time() - kube_cronjob_next_schedule_time > 3600
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Kubernetes CronJob too long
      description: 'CronJob {{ $labels.namespace }}/{{ $labels.cronjob }} is taking
        more than 1h to complete: {{ $value }} seconds'
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kubernetesnamespaceterminationhalted
    title: KubernetesNamespaceTerminationHalted
    condition: A
    for: 2m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: kube_namespace_status_phase{phase="Terminating"} > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Kubernetes Namespace termination halted
      description: "Namespace deletion halted: check the finalizers in spec of  the\
        \ namespace {{ $labels.namespace }}\nIf you sure want to forcely delete namespace\
        \ use the command patern below:\n   kubectl get namespace <stucked-namespace>\
        \ -o json \\\n   | tr -d \"\\n\" | sed \"s/\\\"finalizers\\\": \\[[^]]\\+\\\
        ]/\\\"finalizers\\\": []/\" \\\n   | kubectl replace --raw /api/v1/namespaces/<stucked-namespace>/finalize\
        \ -f -"
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kubernetesendpointaddressnotready
    title: KubernetesEndpointAddressNotReady
    condition: A
    for: 1h
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: kube_endpoint_address_not_ready > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Kubernetes Endpoint address not ready
      description: Endpoint address is not ready during last 5 minutes for endpoint
        {{ $labels.endpoint }} for service {{ $labels.namespace }}/{{ $labels.service
        }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
