apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: node-exporter-folder
spec:
  allowCrossNamespaceImport: true
  title: node-exporter
  instanceSelector:
    matchLabels:
      dashboards: grafana
# ---
# apiVersion: grafana.integreatly.org/v1beta1
# kind: GrafanaAlertRuleGroup
# metadata:
#   name: node-exporter-rules-alert-rules
# spec:
#   allowCrossNamespaceImport: true
#   folderRef: node-exporter-folder
#   instanceSelector:
#     matchLabels:
#       dashboards: grafana
#   interval: 1m
#   rules:
#   - uid: hosthighcpuloadwarning
#     title: HostHighCpuLoadWarning
#     condition: A
#     for: 10m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: Mimir
#       model:
#         datasource:
#           type: prometheus
#           uid: Mimir
#         editorMode: code
#         expr: (sum by (instance) (avg by (mode, instance) (rate(node_cpu_seconds_total{mode!="idle",
#           job!="scylla-db-hosts-sm"}[2m]))) * 100 > 80) * on (instance) group_left
#           (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false        
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host high CPU load warning
#       description: "CPU load on node {{ $labels.nodename }} with ip {{ $labels.instance\
#         \ }} is high > 80%\n  CurrentValue = {{ $value }}%"
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hosthighcpuloadcritical
#     title: HostHighCpuLoadCritical
#     condition: A
#     for: 10m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       model:
#         datasource:
#           type: prometheus          
#         editorMode: code
#         expr: (sum by (instance) (avg by (mode, instance) (rate(node_cpu_seconds_total{mode!="idle",
#           job!="scylla-db-hosts-sm"}[2m]))) * 100 > 90) * on (instance) group_left
#           (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#       #datasourceUid: prometheus
#     labels:
#       severity: critical
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host high CPU load is critical
#       description: "CPU load on node {{ $labels.nodename }} with ip {{ $labels.instance\
#         \ }} is criticaly high > 90% for 10 minutes\n  CurrentValue = {{ $value }}%"
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: scylladbhosthighcpuloadwarning
#     title: ScyllaDBHostHighCpuLoadWarning
#     condition: A
#     for: 15m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       #datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus          
#         editorMode: code
#         expr: (sum by (instance) (avg by (mode, instance) (rate(node_cpu_seconds_total{mode!="idle",
#           job="scylla-db-hosts-sm"}[2m]))) * 100 > 80) * on (instance) group_left
#           (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: ScyllaDB host high CPU load warning
#       description: "ScyllaDB CPU load on node {{ $labels.nodename }} with ip {{ $labels.instance\
#         \ }} is high > 80%\n  CurrentValue = {{ $value }}%"
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: scylladbhosthighcpuloadcritical
#     title: ScyllaDBHostHighCpuLoadCritical
#     condition: A
#     for: 15m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: (sum by (instance) (avg by (mode, instance) (rate(node_cpu_seconds_total{mode!="idle",
#           job="scylla-db-hosts-sm"}[2m]))) * 100 > 90) * on (instance) group_left
#           (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: critical
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: ScyllaDB host high CPU load is critical
#       description: "ScyllaDB CPU load on node {{ $labels.nodename }} with ip {{ $labels.instance\
#         \ }} is criticaly high > 90% for 10 minutes\n  CurrentValue = {{ $value }}%"
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostcpuhighiowait
#     title: HostCpuHighIowait
#     condition: A
#     for: 10m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: (avg by (instance) (rate(node_cpu_seconds_total{mode="iowait"}[5m]))
#           * 100 > 50) * on(instance) group_left (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host CPU high iowait
#       description: CPU iowait > 50% on node {{ $labels.nodename }} with ip {{ $labels.instance
#         }}. A high iowait means that you are disk or network bound.
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostmemoryisunderutilized
#     title: HostMemoryIsUnderutilized
#     condition: A
#     for: 168h
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: (100 - (avg_over_time(node_memory_MemAvailable_bytes[30m]) / node_memory_MemTotal_bytes
#           * 100) < 20) * on(instance) group_left (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: info
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host Memory is underutilized
#       description: 'Node {{ $labels.nodename }} with ip {{ $labels.instance }} memory
#         is < 20% for 1 week. Current usage: {{ $value }}%. Consider reducing memory
#         space.'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostmemoryusagewarning
#     title: HostMemoryUsageWarning
#     condition: A
#     for: 5m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes * 100 <
#           20) * on(instance) group_left (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Node memory usage warning
#       description: Node {{ $labels.nodename }} with ip {{ $labels.instance }} and
#         service label {{ $labels.service }} is using more than 90% RAM. Current free
#         value is {{ $value }}%
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostmemoryusagecritical
#     title: HostMemoryUsageCritical
#     condition: A
#     for: 1m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes * 100 <
#           10) * on(instance) group_left (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: critical
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Node memory usage critical
#       description: Node {{ $labels.nodename }} with ip {{ $labels.instance }} and  service
#         label {{ $labels.service }} is using more than 90% RAM. Current free value
#         is {{ $value }}%
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostoutofdiskspacewarning
#     title: HostOutOfDiskSpaceWarning
#     condition: A
#     for: 2m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: ((node_filesystem_avail_bytes{job !~ "postgres-.*", job!~"scylla-*",
#           service!~".*redis.*"} * 100) / node_filesystem_size_bytes{job !~ "postgres-.*",
#           job!~"scylla-*", service!~".*redis.*"} < 20 and on (instance, device, mountpoint)
#           node_filesystem_readonly == 0) * on(instance) group_left (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Disk is almost full warning
#       description: 'Disk is almost full (< 20% left) on node {{ $labels.nodename }}
#         with ip {{ $labels.instance }}. Currently free: {{ $value }}%'
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostoutofdiskspacecritical
#     title: HostOutOfDiskSpaceCritical
#     condition: A
#     for: 2m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: ((node_filesystem_avail_bytes{job !~ "postgres-.*", job!~"scylla-*",
#           service!~".*redis.*"} * 100) / node_filesystem_size_bytes{job !~ "postgres-.*",
#           job!~"scylla-*", service!~".*redis.*"} < 10 and on (instance, device, mountpoint)
#           node_filesystem_readonly == 0) * on(instance) group_left (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: critical
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Disk is critically full
#       description: 'Disk is almost full (< 10% left) on node {{ $labels.nodename }}
#         with ip {{ $labels.instance }}. Currently free: {{ $value }}%'
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostoutofdiskspacewarningdbs
#     title: HostOutOfDiskSpaceWarningDBs
#     condition: A
#     for: 2m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: ((node_filesystem_avail_bytes{job =~ "postgres-.*", job =~ "scylla-*"}
#           * 100) / node_filesystem_size_bytes{job =~ "postgres-.*", job =~ "scylla-*"}
#           < 20 and on (instance, device, mountpoint) node_filesystem_readonly == 0)
#           * on (instance) group_left (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DBA
#       component: Database
#       alert_type: db-disk-alerts
#     annotations:
#       summary: Disk is almost full warning
#       description: 'Disk is almost full (< 20% left) on node {{ $labels.nodename }}
#         with ip {{ $labels.instance }}. Currently free: {{ $value }}%'
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostoutofdiskspacecriticaldbs
#     title: HostOutOfDiskSpaceCriticalDBs
#     condition: A
#     for: 2m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: ((node_filesystem_avail_bytes{job =~ "postgres-.*", job =~ "scylla-*"}
#           * 100) / node_filesystem_size_bytes{job =~ "postgres-.*", job =~ "scylla-*"}
#           < 10 and on (instance, device, mountpoint) node_filesystem_readonly == 0)
#           * on (instance) group_left (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: critical
#       department: DBA
#       component: Database
#       alert_type: db-disk-alerts
#     annotations:
#       summary: Disk is critically full
#       description: 'Disk is almost full (< 10% left) on node {{ $labels.nodename }}
#         with ip {{ $labels.instance }}. Currently free: {{ $value }}%'
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostoutofdiskspacepostgreremotewarning
#     title: HostOutOfDiskSpacePostgreRemoteWarning
#     condition: A
#     for: 2m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: (node_filesystem_avail_bytes{instance="*************:9100", mountpoint="/"}
#           < 5 * 1024^4 and on (instance, device, mountpoint) node_filesystem_readonly
#           == 0) * on (instance) group_left (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#       alert_type: db-disk-alerts
#     annotations:
#       summary: Disk is almost full on PostgreSQL Remote DB 2 warning
#       description: Disk is almost full (< 5TB) on node {{ $labels.nodename }}  with
#         ip  {{ $labels.instance }}
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostoutofdiskspacepostgreremotecritical
#     title: HostOutOfDiskSpacePostgreRemoteCritical
#     condition: A
#     for: 2m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: (node_filesystem_avail_bytes{instance="*************:9100", mountpoint="/"}
#           < 4 * 1024^4 and on (instance, device, mountpoint) node_filesystem_readonly
#           == 0) * on (instance) group_left (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: critical
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Disk is almost full on PostgreSQL Remote DB 2 critical
#       description: Disk is critically full (< 4TB) on node {{ $labels.nodename }}  with
#         ip  {{ $labels.instance }}
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: ewsredishostredisoutofdiskspacecritical
#     title: EWSRedisHostRedisOutOfDiskSpaceCritical
#     condition: A
#     for: 0s
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: (node_filesystem_avail_bytes{service=~".*redis.*"} * 100) / node_filesystem_size_bytes{service=~".*redis.*"}
#           < 30
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: critical
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Disk is almost full critical
#       description: Disk is almost full (< 30% left) for Redis Service = {{ $labels.service
#         }} on Instance = {{ $labels.instance }}
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: ewsredishostoutofdiskspacewarning
#     title: EWSRedisHostOutOfDiskSpaceWarning
#     condition: A
#     for: 0s
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: (node_filesystem_avail_bytes{service=~".*redis.*"} * 100) / node_filesystem_size_bytes{service=~".*redis.*"}
#           < 40
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Redis Disk is almost full
#       description: Disk is almost full (< 40% left) for Redis Service = {{ $labels.service
#         }} on Instance = {{ $labels.instance }}
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: ews-postgre-disk-space-warn
#     title: EWSPostgreRemoteDatabaseOutOfDiskSpaceWarning
#     condition: A
#     for: 2m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: node_filesystem_avail_bytes{instance="*************:9100", service="postgres-sm",
#           mountpoint = "/"} / 1024 / 1024 / 1024 / 1024 < 2
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Disk on postgres remote db is almost full
#       description: 'Disk on postgres remote database is almost full (2 TB left). Current
#         free space in TB is: {{ $value }}'
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: ews-postgre-disk-space-crit
#     title: EWSRPostgreRemoteDatabaseOutOfDiskSpaceCritical
#     condition: A
#     for: 0s
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: node_filesystem_avail_bytes{instance="*************:9100", service="postgres-sm",
#           mountpoint = "/"} / 1024 / 1024 / 1024 / 1024 < 1
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Disk on postgres remote db is critical full
#       description: 'Disk on postgres remote database is critical full (1 TB left).
#         Current free space in TB is: {{ $value }}'
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostmemoryundermemorypressure
#     title: HostMemoryUnderMemoryPressure
#     condition: A
#     for: 2m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: (rate(node_vmstat_pgmajfault[1m]) > 1000) * on(instance) group_left
#           (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host memory under memory pressure
#       description: 'The node {{ $labels.nodename }} with ip  {{ $labels.instance }}
#         is under heavy memory pressure during last 2 minutes. High rate of major page
#         faults currentValue: {{ $value }}'
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostunusualnetworkthroughputin
#     title: HostUnusualNetworkThroughputIn
#     condition: A
#     for: 5m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: (sum by (instance) (rate(node_network_receive_bytes_total[2m])) / 1024
#           / 1024 > 500) * on(instance) group_left (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host unusual network throughput in
#       description: Host network interfaces are probably receiving too much data (>
#         500 MB/s) on node {{ $labels.nodename }} with ip  {{ $labels.instance }} for
#         last 5 minutes
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostunusualnetworkthroughputout
#     title: HostUnusualNetworkThroughputOut
#     condition: A
#     for: 5m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: (sum by (instance) (rate(node_network_transmit_bytes_total[2m])) / 1024
#           / 1024 > 500) * on(instance) group_left (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host unusual network throughput out
#       description: Host network interfaces are probably sending too much data (> 500
#         MB/s) on node {{ $labels.nodename }} with ip {{ $labels.instance }} during
#         last 5 minutes
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostdiskreadratehigh
#     title: HostDiskReadRateHigh
#     condition: A
#     for: 30m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: (sum by (instance) (rate(node_disk_read_bytes_total[2m])) / 1024 / 1024
#           > 1000) * on(instance) group_left (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host unusual high disk read rate
#       description: Disk is reading too much data (> 1000 MB/s) on node {{ $labels.nodename
#         }} with ip {{ $labels.instance }} during last 2 minutes
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostdiskwriteratehigh
#     title: HostDiskWriteRateHigh
#     condition: A
#     for: 30m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: (sum by (instance) (rate(node_disk_written_bytes_total[2m])) / 1024
#           / 1024 > 500) * on(instance) group_left (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host unusual high disk write rate
#       description: Disk is writing too much data (> 500 MB/s) on node {{ $labels.nodename
#         }} with ip {{ $labels.instance }} during last 2 minutes
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostdiskwillfillin24hours
#     title: HostDiskWillFillIn24Hours
#     condition: A
#     for: 1h
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: ((node_filesystem_avail_bytes * 100) / node_filesystem_size_bytes <
#           10 and ON (instance, device, mountpoint) predict_linear(node_filesystem_avail_bytes{fstype!~"tmpfs"}[1h],
#           24 * 3600) < 0 and ON (instance, device, mountpoint) node_filesystem_readonly
#           == 0) * on(instance) group_left (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: critical
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host disk will fill in 24 hours
#       description: Filesystem is predicted to run out of space within the next 24
#         hours at current write rate on node {{ $labels.nodename }} with ip {{ $labels.instance
#         }} for device {{ $labels.device }}
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostoutofinodes
#     title: HostOutOfInodes
#     condition: A
#     for: 2m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: (node_filesystem_files_free / node_filesystem_files * 100 < 10 and ON
#           (instance, device, mountpoint) node_filesystem_readonly == 0) * on(instance)
#           group_left (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: critical
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host out of inodes
#       description: Disk is almost running out of available inodes (< 10% left) on
#         node {{ $labels.nodename }} with ip {{ $labels.instance }}
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostinodeswillfillin24hours
#     title: HostInodesWillFillIn24Hours
#     condition: A
#     for: 2m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: (node_filesystem_files_free / node_filesystem_files * 100 < 10 and predict_linear(node_filesystem_files_free[1h],
#           24 * 3600) < 0 and ON (instance, device, mountpoint) node_filesystem_readonly
#           == 0) * on(instance) group_left (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host inodes will fill in 24 hours
#       description: Filesystem is predicted to run out of inodes within the next 24
#         hours at current write rate on node {{ $labels.nodename }} with ip {{ $labels.instance
#         }}
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostdiskreadlatencyhigh
#     title: HostDiskReadLatencyHigh
#     condition: A
#     for: 2m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: (rate(node_disk_read_time_seconds_total[1m]) / rate(node_disk_reads_completed_total[1m])
#           > 0.1 and rate(node_disk_reads_completed_total[1m]) > 0) * on(instance)
#           group_left (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host high disk read latency
#       description: Disk latency is growing (read operations > 100ms) on node {{ $labels.nodename
#         }} with ip {{ $labels.instance }} during last 2 minutes
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostdiskwritelatencyhigh
#     title: HostDiskWriteLatencyHigh
#     condition: A
#     for: 2m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: (rate(node_disk_write_time_seconds_total[1m]) / rate(node_disk_writes_completed_total[1m])
#           > 0.1 and rate(node_disk_writes_completed_total[1m]) > 0) * on(instance)
#           group_left (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host unusual disk write latency
#       description: Disk latency is growing (write operations > 100ms) on node {{ $labels.nodename
#         }} with ip {{ $labels.instance }} during last 2 minutes
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostcpustealnoisyneighbor
#     title: HostCpuStealNoisyNeighbor
#     condition: A
#     for: 0s
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: (avg by(instance) (rate(node_cpu_seconds_total{mode="steal"}[5m])) *
#           100 > 10) * on(instance) group_left (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host CPU steal noisy neighbor
#       description: CPU steal is > 10%. A noisy neighbor is killing VM performances
#         or a spot instance may be out of credit on node {{ $labels.nodename }} with
#         ip {{ $labels.instance }}
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostldiskiohigh
#     title: HostlDiskIoHigh
#     condition: A
#     for: 5m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: (rate(node_disk_io_time_seconds_total[1m]) > 1.001) * on(instance) group_left
#           (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: critical
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host unusual disk IO
#       description: Time spent in IO is too high on node {{ $labels.nodename }} with
#         ip {{ $labels.instance }} for device {{ $labels.device }} during last 5 minutes.
#         Check storage for issues.
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostcontextswitchinghigh
#     title: HostContextSwitchingHigh
#     condition: A
#     for: 0s
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: '(rate(node_context_switches_total[15m])/count without(mode,cpu) (node_cpu_seconds_total{mode="idle"}))

#           /

#           (rate(node_context_switches_total[1d])/count without(mode,cpu) (node_cpu_seconds_total{mode="idle"}))
#           > 20'
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host context switching high
#       description: Context switching is growing on the node (more than daily average
#         during the last 15m) on instance {{ $labels.instance }}
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostkernelversiondeviations
#     title: HostKernelVersionDeviations
#     condition: A
#     for: 6h
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: (count(sum(label_replace(node_uname_info, "kernel", "$1", "release",
#           "([0-9]+.[0-9]+.[0-9]+).*")) by (kernel)) > 1) * on(instance) group_left
#           (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host kernel version deviations
#       description: Different kernel versions are running on node {{ $labels.nodename
#         }} with ip  {{ $labels.instance }} observed for a last 6h
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostoomkilldetected
#     title: HostOomKillDetected
#     condition: A
#     for: 0s
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: (increase(node_vmstat_oom_kill{pod=""}[1m]) > 0) * on(instance) group_left
#           (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host OOM kill detected
#       description: OOM kill detected on node {{ $labels.nodename }} with ip {{ $labels.instance
#         }}
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostnetworkreceiveerrors
#     title: HostNetworkReceiveErrors
#     condition: A
#     for: 2m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: increase(node_network_receive_errs_total[2m]) > 0
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host Network Receive Errors
#       description: Host {{ $labels.instance }} interface {{ $labels.device }} has
#         encountered {{ printf "%.0f" $value }} receive errors in the last two minutes.
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostnetworktransmiterrors
#     title: HostNetworkTransmitErrors
#     condition: A
#     for: 2m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: increase(node_network_transmit_errs_total[2m]) > 0
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host Network Transmit Errors
#       description: Host {{ $labels.instance }} interface {{ $labels.device }} has
#         encountered {{ printf "%.0f" $value }} transmit errors in the last two minutes.
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostnetworkinterfacesaturated
#     title: HostNetworkInterfaceSaturated
#     condition: A
#     for: 1m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: (rate(node_network_receive_bytes_total{device!~"^tap.*|^vnet.*|^veth.*|^tun.*"}[1m])
#           + rate(node_network_transmit_bytes_total{device!~"^tap.*|^vnet.*|^veth.*|^tun.*"}[1m]))
#           / node_network_speed_bytes{device!~"^tap.*|^vnet.*|^veth.*|^tun.*"} > 0.8
#           < 10000
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host Network Interface Saturated
#       description: The network interface on {{ $labels.instance }} is getting overloaded.
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostconntracklimit
#     title: HostConntrackLimit
#     condition: A
#     for: 2m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: node_nf_conntrack_entries / node_nf_conntrack_entries_limit > 0.8
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: critical
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host conntrack high
#       description: The number of conntrack is approaching limit on instance {{ $labels.instance
#         }}
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostclockskew
#     title: HostClockSkew
#     condition: A
#     for: 10m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: (node_timex_offset_seconds > 0.05 and deriv(node_timex_offset_seconds[5m])
#           >= 0) or (node_timex_offset_seconds < -0.05 and deriv(node_timex_offset_seconds[5m])
#           <= 0)
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host clock skew detected
#       description: Clock skew detected. Clock is out of sync on instance {{ $labels.instance
#         }}. Ensure NTP is configured correctly on this host.
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostclocknotsynchronising
#     title: HostClockNotSynchronising
#     condition: A
#     for: 2m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: (min_over_time(node_timex_sync_status[1m]) == 0 and node_timex_maxerror_seconds
#           >= 16) * on(instance) group_left (nodename) node_uname_info
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host clock not synchronising
#       description: Clock not synchronising on node {{ $labels.nodename }} with ip
#         {{ $labels.instance }}. Ensure NTP is configured on this host.
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostlowentropypool
#     title: HostLowEntropyPool
#     condition: A
#     for: 5m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: node_entropy_available_bits < 256
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host low entropy pool
#       description: The available entropy pool has dropped below 256 bits for more
#         than 5 minutes on instance {{ $labels.instance }}. Low entropy can cause delays
#         in cryptographic operations.
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostfiledescriptorsexhausted
#     title: HostFileDescriptorsExhausted
#     condition: A
#     for: 0s
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: node_filefd_allocated / node_filefd_maximum > 0.95
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: critical
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host file descriptors nearly exhausted
#       description: Allocated file descriptors exceed 95% of the maximum. The system
#         may soon be unable to open new files or sockets on instance {{ $labels.instance
#         }}.
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostsoftnetpacketdrops
#     title: HostSoftnetPacketDrops
#     condition: A
#     for: 0s
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: increase(node_softnet_dropped_total[2m]) > 0
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host kernel packet backlog drop counter increased
#       description: SoftNetPacketsDrops counter increased on instance {{ $labels.instance
#         }} , which may indicate network congestion or driver issues.
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostipvsconnectionsurge
#     title: HostIpvsConnectionSurge
#     condition: A
#     for: 5m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: increase(node_ipvs_connections_total[5m]) > 80000
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: IPVS connection surge
#       description: More than 80000 new IPVS connections in the last 5 minutes on {{
#         $labels.instance }}. This may indicate a sudden traffic spike or a potential
#         DoS.
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostiostallshigh
#     title: HostIOStallsHigh
#     condition: A
#     for: 15m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: rate(node_pressure_io_stalled_seconds_total[5m]) > 0.8
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: High I/O stalls
#       description: I/O operations are frequently stalled (>0.80s/s for at least 15
#         minutes). Check for slow disks or high load on instance instance {{ $labels.instance
#         }}.
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostxfsdirectorycreatessurge
#     title: HostXFSDirectoryCreatesSurge
#     condition: A
#     for: 10m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: rate(node_xfs_directory_operation_create_total[5m]) > 100
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Surge in XFS directory creations
#       description: More than 100 directory creates per second on XFS for over 10 minutes
#         on instance {{ $labels.instance }}, possibly indicating abnormal application
#         behavior.
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostnetworklinkflapping
#     title: HostNetworkLinkFlapping
#     condition: A
#     for: 5m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: (rate(node_network_carrier_down_changes_total[5m]) + rate(node_network_carrier_up_changes_total[5m]))
#           > 5
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Network link flapping
#       description: The network link has changed state more than 5 times in 5 minutes
#         on instance {{ $labels.instance }}. Check cable, NIC, or switch for issues.
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostexcessivetcpsockets
#     title: HostExcessiveTcpSockets
#     condition: A
#     for: 5m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: node_sockstat_TCP_inuse > 20000
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Excessive TCP sockets in use
#       description: Over 20,000 TCP sockets open for over 5 minutes on {{ $labels.instance
#         }}, suggesting a potential connection leak or abnormal traffic pattern.
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostpowersupplyoffline
#     title: HostPowerSupplyOffline
#     condition: A
#     for: 2m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: node_power_supply_online == 0
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: critical
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: Host power supply offline
#       description: The primary power supply is offline on {{ $labels.instance }} for
#         at least 2 minutes. The host may be on battery or secondary power.
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
#   - uid: hostprocessesblocked
#     title: HostProcessesBlocked
#     condition: A
#     for: 5m
#     data:
#     - refId: A
#       relativeTimeRange:
#         from: 300
#         to: 0
#       datasourceUid: prometheus
#       model:
#         datasource:
#           type: prometheus
#           uid: prometheus
#         editorMode: code
#         expr: node_procs_blocked > 100
#         instant: true
#         intervalMs: 1000
#         legendFormat: __auto
#         maxDataPoints: 43200
#         range: false
#         refId: A
#     labels:
#       severity: warning
#       department: DevOps
#       component: Infrastructure
#     annotations:
#       summary: High number of blocked processes
#       description: More than 100 processes are blocked for at least 5 minutes on {{
#         $labels.instance }}), possibly due to I/O wait or kernel resource contention.
#       value: '{{ $value }}'
#     noDataState: NoData
#     execErrState: Error
#     isPaused: false
