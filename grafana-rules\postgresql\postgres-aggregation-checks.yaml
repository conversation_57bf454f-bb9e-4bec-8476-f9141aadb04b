apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: postgresql-folder
spec:
  allowCrossNamespaceImport: true
  title: postgresql
  instanceSelector:
    matchLabels:
      dashboards: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: postgres-aggregation-rules-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: postgresql-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana
  interval: 1m
  rules:
  - uid: casinogamedimensionaggregatemissed
    title: CasinoGameDimensionAggregateMissed
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: time() - LastAggregationRun{Aggregation="casino_game_dimension_aggregate"}
          > 4200
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: Postgres
    annotations:
      summary: casino_game_dimension_aggregate_Missed
      description: casino_game_dimension_aggregate not run in more than an hour and
        a half
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: casinoplayerdimensionaggregatemissed
    title: CasinoPlayerDimensionAggregateMissed
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: time() - LastAggregationRun{Aggregation="casino_player_dimension_aggregate"}
          > 4200
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: Postgres
    annotations:
      summary: casino_player_dimension_aggregate_Missed
      description: casino_player_dimension_aggregate not run in more than an hour
        and a half
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: casinoplayergamedimensionaggregatemissed
    title: CasinoPlayerGameDimensionAggregateMissed
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: time() - LastAggregationRun{Aggregation="casino_player_game_dimension_aggregate"}
          > 4200
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: Postgres
    annotations:
      summary: casino_player_game_dimension_aggregate_Missed
      description: casino_player_game_dimension_aggregate not run in more than an
        hour and a half
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: pendingwithdrawsaggregatemissed
    title: PendingWithdrawsAggregateMissed
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: time() - LastAggregationRun{Aggregation="pending_withdraws_aggregate"}
          > 90000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: Postgres
    annotations:
      summary: pending_withdraws_aggregate_Missed
      description: pending_withdraws_aggregate not run in more than 25 hours
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: playerbalanceaggregatemissed
    title: PlayerBalanceAggregateMissed
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: time() - LastAggregationRun{Aggregation="player_balance_aggregate"}
          > 4200
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: Postgres
    annotations:
      summary: player_balance_aggregate_Missed
      description: player_balance_aggregate not run in more than an hour and a half
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: sportplayerdimensionaggregatemissed
    title: SportPlayerDimensionAggregateMissed
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: time() - LastAggregationRun{Aggregation="sport_player_dimension_aggregate"}
          > 4200
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: Postgres
    annotations:
      summary: sport_player_dimension_aggregate_Missed
      description: sport_player_dimension_aggregate not run in more than an hour and
        a half
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: postgresaggregationmissedtotalfunds
    title: PostgresAggregationMissedTotalFunds
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: time() - LastAggregationRun{Aggregation="total_funds_dimension_aggregate_cron"}
          > 4200
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: Postgres
    annotations:
      summary: Postgres Aggregation Missed for total funds cron
      description: total_funds_dimension_aggregate_cron not run in more than an hour
        and a half
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: postgresaggregationerror
    title: PostgresAggregationError
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: time() - AggregationErrorsCheck < 86400
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: Postgres
    annotations:
      summary: PostgresAggregationError
      description: Postgres Aggregation {{ $labels.AggregationError }} failed before
        {{ $value }} seconds, please check for possible errors
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: postgresaggregationtooslowalert
    title: PostgresAggregationTooSlowAlert
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: AggregationDurationCheck > 1500
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: Postgres
    annotations:
      summary: Postgres Aggregation duration too slow
      description: Postgres Aggregation {{ $labels.AggregationDuration }} is taking
        too much time! Started before {{ $value }} seconds!
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
