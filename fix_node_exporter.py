#!/usr/bin/env python3
"""
Comprehensive fix for node-exporter rules to ensure all conditions are properly set to A
and the YAML structure is clean.
"""

import yaml
import os

def fix_node_exporter_rules():
    file_path = r'c:\!NDI\!!!!EGT\repos\observability-rules\grafana-rules\node-exporter\node-exporter-rules.yaml'
    
    print(f"Processing: {file_path}")
    
    try:
        # Read the file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse YAML documents
        documents = list(yaml.safe_load_all(content))
        
        changes_made = False
        
        for doc in documents:
            if doc and doc.get('kind') == 'GrafanaAlertRuleGroup':
                spec = doc.get('spec', {})
                rules = spec.get('rules', [])
                
                for i, rule in enumerate(rules):
                    rule_uid = rule.get('uid', f'rule-{i}')
                    print(f"  Processing rule {i}: {rule_uid}")
                    
                    # Ensure condition is A
                    if rule.get('condition') != 'A':
                        rule['condition'] = 'A'
                        changes_made = True
                        print(f"    Fixed condition for {rule_uid}")
                    
                    # Ensure data structure is correct
                    data_blocks = rule.get('data', [])
                    if len(data_blocks) > 1:
                        # Keep only the first data block
                        rule['data'] = [data_blocks[0]]
                        changes_made = True
                        print(f"    Removed extra data blocks for {rule_uid}")
                    
                    if len(data_blocks) > 0:
                        first_block = data_blocks[0]
                        
                        # Ensure refId is A
                        if first_block.get('refId') != 'A':
                            first_block['refId'] = 'A'
                            changes_made = True
                            print(f"    Fixed refId for {rule_uid}")
                        
                        # Ensure datasourceUid is set
                        if 'datasourceUid' not in first_block or first_block['datasourceUid'] is None:
                            first_block['datasourceUid'] = 'prometheus'
                            changes_made = True
                            print(f"    Fixed datasourceUid for {rule_uid}")
                        
                        # Ensure model.datasource is properly set
                        if 'model' in first_block and 'datasource' in first_block['model']:
                            datasource = first_block['model']['datasource']
                            if isinstance(datasource, dict):
                                if 'type' not in datasource:
                                    datasource['type'] = 'prometheus'
                                    changes_made = True
                                if 'uid' not in datasource:
                                    datasource['uid'] = first_block.get('datasourceUid', 'prometheus')
                                    changes_made = True
                                    print(f"    Fixed datasource config for {rule_uid}")
        
        if changes_made:
            print("  Changes were made, writing file...")
            
            # Write back with proper formatting
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump_all(documents, f, default_flow_style=False, sort_keys=False, width=1000)
            
            print("  File updated successfully!")
        else:
            print("  No changes needed")
        
        # Verify the file
        print("\nVerifying the file...")
        with open(file_path, 'r', encoding='utf-8') as f:
            verify_content = f.read()
        
        verify_docs = list(yaml.safe_load_all(verify_content))
        
        condition_c_count = 0
        condition_a_count = 0
        
        for doc in verify_docs:
            if doc and doc.get('kind') == 'GrafanaAlertRuleGroup':
                rules = doc.get('spec', {}).get('rules', [])
                for rule in rules:
                    condition = rule.get('condition')
                    if condition == 'C':
                        condition_c_count += 1
                        print(f"  WARNING: Found condition C in rule: {rule.get('uid')}")
                    elif condition == 'A':
                        condition_a_count += 1
        
        print(f"Verification complete:")
        print(f"  Rules with condition A: {condition_a_count}")
        print(f"  Rules with condition C: {condition_c_count}")
        
        if condition_c_count == 0:
            print("✅ All conditions are properly set to A!")
        else:
            print("❌ Some conditions are still set to C!")
        
        return condition_c_count == 0
        
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == '__main__':
    success = fix_node_exporter_rules()
    if success:
        print("\n🎉 Node-exporter rules have been successfully fixed!")
        print("You can now try applying the rules in Grafana again.")
    else:
        print("\n❌ There were issues fixing the rules. Please check the output above.")
