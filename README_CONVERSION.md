# Prometheus to Grafana Rules Conversion

This repository contains scripts to convert Prometheus alerting rules to Grafana alerting rules format.

## Overview

The conversion script `convert_prometheus_to_grafana.py` processes all PrometheusRule YAML files from the `prometheus-rules-old` directory and converts them to Grafana-compatible format in the `grafana-rules` directory.

## Features

- **Batch Processing**: Converts all Prometheus rules in the directory structure
- **Structure Preservation**: Maintains the same directory structure in the output
- **Namespace Mapping**: Uses directory names as Grafana folder namespaces
- **Complete Conversion**: Converts alerts, labels, annotations, and expressions
- **File Copying**: Preserves non-YAML files (README.md, kustomization.yaml, etc.)

## Prerequisites

Install the required dependencies:

```bash
pip install -r requirements.txt
```

## Usage

### Convert All Rules

Run the conversion script from the repository root:

```bash
python convert_prometheus_to_grafana.py
```

This will:
1. Process all `.yaml` and `.yml` files in `prometheus-rules-old/`
2. Create corresponding Grafana rules in `grafana-rules/`
3. Maintain the same directory structure
4. Copy non-YAML files to preserve structure

### Convert Single File (using original script)

For single file conversion, you can use the original script:

```bash
python gpt-python1.py input_prometheus_rule.yaml > output_grafana_crds.yaml
```

## Output Structure

The conversion creates two types of Grafana CRDs for each PrometheusRule:

1. **GrafanaFolder**: Organizes alerts into folders
2. **GrafanaAlertRuleGroup**: Contains the actual alert rules

### Example Conversion

**Input** (`prometheus-rules-old/artifactory/artifactory-rules.yaml`):
```yaml
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: artifactory-rules
  namespace: infrastructure
spec:
  groups:
  - name: artifactory
    rules:
    - alert: ArtifactoryDown
      expr: artifactory_up == 0
      labels:
        severity: critical
      annotations:
        summary: Artifactory is down
```

**Output** (`grafana-rules/artifactory/artifactory-rules-grafana.yaml`):
```yaml
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: artifactory-folder
spec:
  title: artifactory
  instanceSelector:
    matchLabels:
      app: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: artifactory-rules-alert-rules
spec:
  folderRef: artifactory-folder
  instanceSelector:
    matchLabels:
      app: grafana
  interval: 1m
  rules:
  - uid: artifactorydown
    title: ArtifactoryDown
    condition: C
    for: 0s
    data: [...]
    labels:
      severity: critical
    annotations:
      summary: Artifactory is down
```

## Conversion Details

### Query Structure

Each Prometheus alert expression is converted to a Grafana query chain:

1. **Query (A)**: Original Prometheus expression
2. **Reduce (B)**: Reduces the query result to a single value
3. **Threshold (C)**: Evaluates if the value meets alert conditions

### Namespace Mapping

- Directory names become Grafana folder namespaces
- Example: `prometheus-rules-old/artifactory/` → `artifactory-folder`

### File Naming

- Input: `{name}-rules.yaml`
- Output: `{name}-rules-grafana.yaml`

## Supported Features

- ✅ Alert rules conversion
- ✅ Labels and annotations preservation
- ✅ Expression conversion
- ✅ For duration mapping
- ✅ Directory structure preservation
- ✅ Batch processing
- ✅ Error handling and logging

## Notes

- Only PrometheusRule files are converted (other YAML files are copied as-is)
- Recording rules are skipped (only alert rules are converted)
- Datasource UID is set to 'prometheus' (adjust if needed)
- Instance selector uses `app: grafana` label

## Troubleshooting

If you encounter issues:

1. Ensure all YAML files are valid
2. Check that the `prometheus-rules-old` directory exists
3. Verify Python dependencies are installed
4. Check file permissions for output directory creation

## File Structure

```
├── convert_prometheus_to_grafana.py  # Main conversion script
├── gpt-python1.py                   # Single file conversion script
├── requirements.txt                 # Python dependencies
├── prometheus-rules-old/            # Source Prometheus rules
│   ├── artifactory/
│   ├── kafka/
│   └── ...
└── grafana-rules/                   # Generated Grafana rules
    ├── artifactory/
    ├── kafka/
    └── ...
```
