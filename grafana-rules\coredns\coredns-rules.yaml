apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: coredns-folder
spec:
  allowCrossNamespaceImport: true
  title: coredns
  instanceSelector:
    matchLabels:
      dashboards: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: coredns-rules-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: coredns-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana
  interval: 1m
  rules:
  - uid: corednsdown
    title: CoreDNSDown
    condition: C
    for: 15m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: absent(up{job="coredns"} == 1)
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: Kubernetes
      department: DevOps
    annotations:
      summary: CoreDNS has disappeared from Prometheus target discovery.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: corednslatencyhigh
    title: CoreDNSLatencyHigh
    condition: C
    for: 10m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: histogram_quantile(0.99, sum(rate(coredns_dns_request_duration_seconds_bucket{job="coredns-vlan"}[5m]))
          by(server, zone, le)) > 0.5
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: Kubernetes
      department: DevOps
    annotations:
      summary: CoreDNS latency high
      description: CoreDNS has 99th percentile latency of {{ $value }} seconds for
        server {{ $labels.server }} zone {{ $labels.zone }} .
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: corednserrorshigh
    title: CoreDNSErrorsHigh
    condition: C
    for: 10m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(rate(coredns_dns_responses_total{job="coredns-vlan",rcode="SERVFAIL"}[5m]))
          / sum(rate(coredns_dns_responses_total{job="coredns-vlan"}[5m])) > 0.03
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: Kubernetes
      department: DevOps
    annotations:
      summary: CoreDNS errors high
      description: CoreDNS is returning SERVFAIL for {{ $value | humanizePercentage
        }} of requests.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: corednserrorshigh
    title: CoreDNSErrorsHigh
    condition: C
    for: 10m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(rate(coredns_dns_responses_total{job="coredns-vlan",rcode="SERVFAIL"}[5m]))
          / sum(rate(coredns_dns_responses_total{job="coredns-vlan"}[5m])) > 0.01
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: Kubernetes
      department: DevOps
    annotations:
      summary: CoreDNS errors high
      description: CoreDNS is returning SERVFAIL for {{ $value | humanizePercentage
        }} of requests.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: corednsforwardlatencyhigh
    title: CoreDNSForwardLatencyHigh
    condition: C
    for: 10m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: histogram_quantile(0.99, sum(rate(coredns_forward_request_duration_seconds_bucket{job="coredns-vlan"}[5m]))
          by(to, le)) > 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: Kubernetes
      department: DevOps
    annotations:
      summary: CoreDNS forward latency high
      description: CoreDNS has 99th percentile latency of {{ $value }} seconds forwarding
        requests to {{ $labels.to }}.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: corednsforwarderrorshigh
    title: CoreDNSForwardErrorsHigh
    condition: C
    for: 10m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(rate(coredns_forward_responses_total{job="coredns-vlan",rcode="SERVFAIL"}[5m]))
          / sum(rate(coredns_forward_responses_total{job="coredns-vlan"}[5m])) > 0.03
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: Kubernetes
      department: DevOps
    annotations:
      summary: CoreDNS forward errors high
      description: CoreDNS is returning SERVFAIL for {{ $value | humanizePercentage
        }} of forward requests to {{ $labels.to }}.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: corednsforwarderrorshigh
    title: CoreDNSForwardErrorsHigh
    condition: C
    for: 10m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(rate(coredns_forward_responses_total{job="coredns-vlan",rcode="SERVFAIL"}[5m]))
          / sum(rate(coredns_forward_responses_total{job="coredns-vlan"}[5m])) > 0.01
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: Kubernetes
      department: DevOps
    annotations:
      summary: CoreDNS forward errors high
      description: CoreDNS is returning SERVFAIL for {{ $value | humanizePercentage
        }} of forward requests to {{ $labels.to }}.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: corednsforwardhealthcheckfailurecount
    title: CoreDNSForwardHealthcheckFailureCount
    condition: C
    for: 10m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(rate(coredns_forward_healthcheck_failures_total{job="coredns-vlan"}[5m]))
          by (to) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: Kubernetes
      department: DevOps
    annotations:
      summary: CoreDNS forward healthcheck failure
      description: CoreDNS health checks have failed to upstream server {{ $labels.to
        }}.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: corednsforwardhealthcheckbrokencount
    title: CoreDNSForwardHealthcheckBrokenCount
    condition: C
    for: 10m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(rate(coredns_forward_healthcheck_broken_total{job="coredns-vlan"}[5m]))
          > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: Kubernetes
      department: DevOps
    annotations:
      summary: CoreDNS forward healthcheck broken
      description: CoreDNS health checks have failed for all upstream servers.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
