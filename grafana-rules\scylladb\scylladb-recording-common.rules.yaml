apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: scylladb-folder
spec:
  allowCrossNamespaceImport: true
  title: scylladb
  instanceSelector:
    matchLabels:
      dashboards: "grafana"
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: scylladb-common-recording-rules-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: scylladb-folder
  instanceSelector:
    matchLabels:
      dashboards: "grafana"
  interval: 1m
  rules: []
