apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: ssl-checks-folder
spec:
  allowCrossNamespaceImport: true
  title: ssl-checks
  instanceSelector:
    matchLabels:
      dashboards: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: ssl-alarms-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: ssl-checks-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana
  interval: 1m
  rules:
  - uid: sslexpire30days
    title: SslExpire30days
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: probe_ssl_earliest_cert_expiry - time() < 86400 * 30
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: infrastructure
      department: DevOps
    annotations:
      summary: SSL certificate expires in 30 days
      description: SSL for {{ $labels.target }} will expire in less than 30 days
      value: '{{ $value }}'
      runbook_url: https://confluence.egt-digital.com/display/K8S/ssl-checks
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: sslexpire10days
    title: SslExpire10days
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: probe_ssl_earliest_cert_expiry - time() < 86400 * 10
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: infrastructure
      department: DevOps
    annotations:
      summary: SSL certificate expires in 10 days
      description: SSL for {{ $labels.target }} will expire in less than 10 days
      value: '{{ $value }}'
      runbook_url: https://confluence.egt-digital.com/display/K8S/ssl-checks
    noDataState: NoData
    execErrState: Error
    isPaused: false
