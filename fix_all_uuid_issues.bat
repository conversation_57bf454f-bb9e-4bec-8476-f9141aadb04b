@echo off
echo Fixing all Grafana sync issues - long UIDs and instanceSelector problems...

REM Fix long UIDs in kubernetes-rules.yaml
echo Fixing kubernetes-rules.yaml...
powershell -Command "(Get-Content 'grafana-rules\kubernetes\kubernetes-rules.yaml') -replace 'uid: kubeproxysyncproxyrulesiptablesrestorefailures', 'uid: kubeproxy-iptables-restore-fail' | Set-Content 'grafana-rules\kubernetes\kubernetes-rules.yaml'"
powershell -Command "(Get-Content 'grafana-rules\kubernetes\kubernetes-rules.yaml') -replace 'uid: kubernetespodcontainerrestartratetoohigh', 'uid: k8s-pod-container-restart-high' | Set-Content 'grafana-rules\kubernetes\kubernetes-rules.yaml'"
powershell -Command "(Get-Content 'grafana-rules\kubernetes\kubernetes-rules.yaml') -replace 'uid: kubernetespodcontainerstatuslastterminatedreason', 'uid: k8s-pod-last-terminated-reason' | Set-Content 'grafana-rules\kubernetes\kubernetes-rules.yaml'"

REM Fix long UIDs in sport/tournaments-service.yaml
echo Fixing sport/tournaments-service.yaml...
powershell -Command "(Get-Content 'grafana-rules\sport\tournaments-service.yaml') -replace 'uid: playertournamentsaggregatedpointsthroughputunsuccessful', 'uid: player-tournaments-points-unsuccessful' | Set-Content 'grafana-rules\sport\tournaments-service.yaml'"

REM Fix long UIDs in prometheus-rules.yaml
echo Fixing prometheus-rules.yaml...
powershell -Command "(Get-Content 'grafana-rules\prometheus\prometheus-rules.yaml') -replace 'uid: prometheusalertmanagerconfigurationreloadfailure', 'uid: prometheus-alertmgr-config-fail' | Set-Content 'grafana-rules\prometheus\prometheus-rules.yaml'"
powershell -Command "(Get-Content 'grafana-rules\prometheus\prometheus-rules.yaml') -replace 'uid: prometheusalertmanagernotificationfailing', 'uid: prometheus-alertmgr-notif-fail' | Set-Content 'grafana-rules\prometheus\prometheus-rules.yaml'"

REM Fix long UIDs in node-exporter-rules.yaml
echo Fixing node-exporter-rules.yaml...
powershell -Command "(Get-Content 'grafana-rules\node-exporter\node-exporter-rules.yaml') -replace 'uid: ewspostgreremotedatabaseoutofdiskspacewarning', 'uid: ews-postgre-disk-space-warn' | Set-Content 'grafana-rules\node-exporter\node-exporter-rules.yaml'"
powershell -Command "(Get-Content 'grafana-rules\node-exporter\node-exporter-rules.yaml') -replace 'uid: ewsrpostgreremotedatabaseoutofdiskspacecritical', 'uid: ews-postgre-disk-space-crit' | Set-Content 'grafana-rules\node-exporter\node-exporter-rules.yaml'"

REM Fix long UIDs in kafka.yaml
echo Fixing kafka.yaml...
powershell -Command "(Get-Content 'grafana-rules\kafka\kafka.yaml') -replace 'uid: toolargeconsumergrouplagbetgeniusconsumer', 'uid: large-consumer-lag-betgenius' | Set-Content 'grafana-rules\kafka\kafka.yaml'"
powershell -Command "(Get-Content 'grafana-rules\kafka\kafka.yaml') -replace 'uid: toolargeconsumergrouplagplayertournaments', 'uid: large-consumer-lag-tournaments' | Set-Content 'grafana-rules\kafka\kafka.yaml'"
powershell -Command "(Get-Content 'grafana-rules\kafka\kafka.yaml') -replace 'uid: toolargeconsumergrouplagfortransactiontopic', 'uid: large-consumer-lag-transaction' | Set-Content 'grafana-rules\kafka\kafka.yaml'"
powershell -Command "(Get-Content 'grafana-rules\kafka\kafka.yaml') -replace 'uid: toolargeconsumergrouplagfeedupdatesbetradar', 'uid: large-consumer-lag-betradar' | Set-Content 'grafana-rules\kafka\kafka.yaml'"
powershell -Command "(Get-Content 'grafana-rules\kafka\kafka.yaml') -replace 'uid: toolargeconsumergrouplagfeedupdatesbetgenius', 'uid: large-consumer-lag-betgenius-feed' | Set-Content 'grafana-rules\kafka\kafka.yaml'"
powershell -Command "(Get-Content 'grafana-rules\kafka\kafka.yaml') -replace 'uid: toolargeconsumergrouplagfeedupdateslsports', 'uid: large-consumer-lag-lsports' | Set-Content 'grafana-rules\kafka\kafka.yaml'"
powershell -Command "(Get-Content 'grafana-rules\kafka\kafka.yaml') -replace 'uid: toolargeconsumergrouplagnotmappedtemplates', 'uid: large-consumer-lag-templates' | Set-Content 'grafana-rules\kafka\kafka.yaml'"
powershell -Command "(Get-Content 'grafana-rules\kafka\kafka.yaml') -replace 'uid: toolargeconsumergrouplagfeedparkedupdates', 'uid: large-consumer-lag-parked' | Set-Content 'grafana-rules\kafka\kafka.yaml'"
powershell -Command "(Get-Content 'grafana-rules\kafka\kafka.yaml') -replace 'uid: kafkanetworksocketserverconnectionskilled', 'uid: kafka-network-connections-killed' | Set-Content 'grafana-rules\kafka\kafka.yaml'"
powershell -Command "(Get-Content 'grafana-rules\kafka\kafka.yaml') -replace 'uid: kafkabrokertopicmetricsfailedfetchrequests', 'uid: kafka-broker-failed-fetch-req' | Set-Content 'grafana-rules\kafka\kafka.yaml'"
powershell -Command "(Get-Content 'grafana-rules\kafka\kafka.yaml') -replace 'uid: kafkabrokertopicmetricsfailedproducerrequests', 'uid: kafka-broker-failed-producer-req' | Set-Content 'grafana-rules\kafka\kafka.yaml'"
powershell -Command "(Get-Content 'grafana-rules\kafka\kafka.yaml') -replace 'uid: kafkabrokertopicmetricsinvalidmagicnumberrecords', 'uid: kafka-broker-invalid-magic-num' | Set-Content 'grafana-rules\kafka\kafka.yaml'"
powershell -Command "(Get-Content 'grafana-rules\kafka\kafka.yaml') -replace 'uid: kafkabrokertopicmetricsinvalidmessagecrcrecords', 'uid: kafka-broker-invalid-msg-crc' | Set-Content 'grafana-rules\kafka\kafka.yaml'"
powershell -Command "(Get-Content 'grafana-rules\kafka\kafka.yaml') -replace 'uid: kafkabrokertopicmetricsinvalidoffsetrecords', 'uid: kafka-broker-invalid-offset' | Set-Content 'grafana-rules\kafka\kafka.yaml'"

REM Fix long UIDs in logstash-rules.yaml
echo Fixing logstash-rules.yaml...
powershell -Command "(Get-Content 'grafana-rules\elastic-stack\logstash-rules.yaml') -replace 'uid: logstashpipelinepluginsoutputsbulkrequestserrorrate', 'uid: logstash-bulk-requests-error-rate' | Set-Content 'grafana-rules\elastic-stack\logstash-rules.yaml'"
powershell -Command "(Get-Content 'grafana-rules\elastic-stack\logstash-rules.yaml') -replace 'uid: logstashpipelinepluginsoutputsdocumentsnonretryableerrorrate', 'uid: logstash-docs-nonretry-error-rate' | Set-Content 'grafana-rules\elastic-stack\logstash-rules.yaml'"
powershell -Command "(Get-Content 'grafana-rules\elastic-stack\logstash-rules.yaml') -replace 'uid: logstashpipelineeventsprocessingtimesinsert', 'uid: logstash-events-processing-insert' | Set-Content 'grafana-rules\elastic-stack\logstash-rules.yaml'"
powershell -Command "(Get-Content 'grafana-rules\elastic-stack\logstash-rules.yaml') -replace 'uid: logstashpipelineeventsprocessingtimesslow', 'uid: logstash-events-processing-slow' | Set-Content 'grafana-rules\elastic-stack\logstash-rules.yaml'"

REM Fix long UIDs in clickhouse-rules.yaml
echo Fixing clickhouse-rules.yaml...
powershell -Command "(Get-Content 'grafana-rules\clickhouse\clickhouse-rules.yaml') -replace 'uid: clickhousedistributedconnectionexceptions', 'uid: clickhouse-distributed-conn-exc' | Set-Content 'grafana-rules\clickhouse\clickhouse-rules.yaml'"
powershell -Command "(Get-Content 'grafana-rules\clickhouse\clickhouse-rules.yaml') -replace 'uid: clickhouseerrormetriccannotallocatememory', 'uid: clickhouse-cannot-allocate-mem' | Set-Content 'grafana-rules\clickhouse\clickhouse-rules.yaml'"
powershell -Command "(Get-Content 'grafana-rules\clickhouse\clickhouse-rules.yaml') -replace 'uid: clickhouseerrormetricreceivederrortoomanyrequests', 'uid: clickhouse-too-many-requests' | Set-Content 'grafana-rules\clickhouse\clickhouse-rules.yaml'"
powershell -Command "(Get-Content 'grafana-rules\clickhouse\clickhouse-rules.yaml') -replace 'uid: clickhouseerrormetricdatabasereplicationfailed', 'uid: clickhouse-db-replication-failed' | Set-Content 'grafana-rules\clickhouse\clickhouse-rules.yaml'"
powershell -Command "(Get-Content 'grafana-rules\clickhouse\clickhouse-rules.yaml') -replace 'uid: clickhouseerrormetrictoomanysimultaneousqueries', 'uid: clickhouse-too-many-queries' | Set-Content 'grafana-rules\clickhouse\clickhouse-rules.yaml'"

echo.
echo ========================================
echo CRITICAL: instanceSelector Immutability Issue
echo ========================================
echo.
echo The instanceSelector field is immutable and cannot be changed once created.
echo You need to delete the existing Grafana resources before reapplying:
echo.
echo kubectl delete grafanafolder kubernetes-folder
echo kubectl delete grafanaalertrulegroup kubernetes-rules-alert-rules
echo.
echo After deletion, you can reapply the configuration with the fixed UIDs.
echo.
echo ========================================
echo Summary of UID Changes:
echo ========================================
echo All UIDs have been shortened to 40 characters or less to comply with Grafana requirements.
echo.
echo Files modified:
echo - kubernetes/kubernetes-rules.yaml (3 UIDs fixed)
echo - sport/tournaments-service.yaml (1 UID fixed)
echo - prometheus/prometheus-rules.yaml (2 UIDs fixed)
echo - node-exporter/node-exporter-rules.yaml (2 UIDs fixed)
echo - kafka/kafka.yaml (12 UIDs fixed)
echo - elastic-stack/logstash-rules.yaml (4 UIDs fixed)
echo - clickhouse/clickhouse-rules.yaml (5 UIDs fixed)
echo.
echo Total: 29 long UIDs have been fixed across 7 files.
echo.
pause
