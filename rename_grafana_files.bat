@echo off
echo Renaming YAML files to remove "-grafana" suffix...

ren "grafana-rules\artifactory\artifactory-rules-grafana.yaml" "artifactory-rules.yaml"
ren "grafana-rules\barman\barman-grafana.yaml" "barman.yaml"
ren "grafana-rules\clickhouse\clickhouse-delay-exporter-grafana.yaml" "clickhouse-delay-exporter.yaml"
ren "grafana-rules\clickhouse\clickhouse-rules-grafana.yaml" "clickhouse-rules.yaml"
ren "grafana-rules\cloudflare\cloudflare-rules-grafana.yaml" "cloudflare-rules.yaml"
ren "grafana-rules\coredns\coredns-rules-grafana.yaml" "coredns-rules.yaml"
ren "grafana-rules\crm\outbound-proxy-rules-grafana.yaml" "outbound-proxy-rules.yaml"
ren "grafana-rules\dataguard\dataguard-grafana.yaml" "dataguard.yaml"
ren "grafana-rules\elastic-stack\elasticsearch-grafana.yaml" "elasticsearch.yaml"
ren "grafana-rules\elastic-stack\logstash-rules-grafana.yaml" "logstash-rules.yaml"
ren "grafana-rules\fluentbit\fluentbit-grafana.yaml" "fluentbit.yaml"
ren "grafana-rules\jenkins\jenkins-rules-grafana.yaml" "jenkins-rules.yaml"
ren "grafana-rules\kafka\kafka-grafana.yaml" "kafka.yaml"
ren "grafana-rules\kubernetes\kubernetes-rules-grafana.yaml" "kubernetes-rules.yaml"
ren "grafana-rules\nginx\nginx-plus-grafana.yaml" "nginx-plus.yaml"
ren "grafana-rules\node-exporter\node-exporter-rules-grafana.yaml" "node-exporter-rules.yaml"
ren "grafana-rules\postgresql\postgres-aggregation-checks-grafana.yaml" "postgres-aggregation-checks.yaml"
ren "grafana-rules\postgresql\postgres-exporter-grafana.yaml" "postgres-exporter.yaml"
ren "grafana-rules\prometheus\prometheus-rules-grafana.yaml" "prometheus-rules.yaml"
ren "grafana-rules\rabbitmq\rabbitmq-rules-grafana.yaml" "rabbitmq-rules.yaml"
ren "grafana-rules\redis\redis-grafana.yaml" "redis.yaml"
ren "grafana-rules\regulatory\nra-checks-grafana.yaml" "nra-checks.yaml"
ren "grafana-rules\scylladb\scylladb-alerting.rules-grafana.yaml" "scylladb-alerting.rules.yaml"
ren "grafana-rules\scylladb\scylladb-recording-common.rules-grafana.yaml" "scylladb-recording-common.rules.yaml"
ren "grafana-rules\scylladb\scylladb-recording-latency.rules-grafana.yaml" "scylladb-recording-latency.rules.yaml"
ren "grafana-rules\sport\tournaments-service-grafana.yaml" "tournaments-service.yaml"
ren "grafana-rules\sport\websocket-net-grafana.yaml" "websocket-net.yaml"
ren "grafana-rules\ssl-checks\ssl-alarms-grafana.yaml" "ssl-alarms.yaml"
ren "grafana-rules\vault\vault-grafana.yaml" "vault.yaml"

echo Done! All files have been renamed to remove the "-grafana" suffix.
pause
