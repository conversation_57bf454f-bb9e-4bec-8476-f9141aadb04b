apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: elastic-stack-folder
spec:
  allowCrossNamespaceImport: true
  title: elastic-stack
  instanceSelector:
    matchLabels:
      dashboards: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: logstash-exporter-cluster-rules-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: elastic-stack-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana
  interval: 1m
  rules:
  - uid: logstashdown
    title: LogstashDown
    condition: A
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: logstash_up != 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Logstash is down
      description: Logstash is down, instance = {{ $labels.instance }} on pod = {{
        $labels.pod }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: logstashcpuhighwarning
    title: LogstashCpuHighWarning
    condition: A
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: logstash_process_cpu_percent > 80
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Logstash CPU usage is high.
      description: "Logstash CPU usage is high : >80% for 5 minutes \n Instance =\
        \ {{ $labels.instance }}\n Pod = {{ $labels.pod }}"
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: logstashcpuhighcritical
    title: LogstashCpuHighCritical
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: logstash_process_cpu_load_average_15m > 90
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Logstash CPU load average is high
      description: "Logstash CPU load average is high : >90% during last 15 minutes\n\
        \ Instance = {{ $labels.instance }}\n Pod = {{ $labels.pod }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: logstashjvmmemoryheaphigh
    title: LogstashJvmMemoryHeapHigh
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: logstash_jvm_mem_heap_used_percent > 90
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Logstash JVM Heap Memory usage is high
      description: "Logstash JVM Heap Memory usage is high : >90% \n Instance = {{\
        \ $labels.instance }}\n Pod = {{ $labels.pod }}"
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: logstashfailurereload
    title: LogstashFailureReload
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: increase(logstash_reloads_failures[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Logstash configuration reload failed
      description: "Logstash configuration reload failed\n Instance = {{ $labels.instance\
        \ }}\n Pipeline = {{ $labels.pipeline }}\n Pod = {{ $labels.pod }}"
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: logstashpipelinefailurereload
    title: LogstashPipelineFailureReload
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: increase(logstash_pipeline_reloads_failures[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Logstash pipeline configuration reload failed
      description: "Logstash pipeline configuration reload failed\n Instance = {{\
        \ $labels.instance }}\n Pipeline = {{ $labels.pipeline }}\n Pod = {{ $labels.pod\
        \ }}"
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: logstash-bulk-requests-error-rate
    title: LogstashPipelinePluginsOutputsBulkRequestsErrorRate
    condition: A
    for: 1h
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: logstash_pipeline_plugins_outputs_bulk_requests_with_errors / logstash_pipeline_plugins_outputs_bulk_requests_successes
          > 2
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Logstash pipeline output bulk requests with errors are too high.
      description: 'Logstash pipeline output bulk requests with errors are abnormal
        higher than succeed bulk requests

        More than x2 of bulk requests are failed compare to succeed

        Instance = {{ $labels.instance }}; Pipeline = {{ $labels.pipeline }}

        Pod = {{ $labels.pod }};  Name = {{ $labels.name }};  id = {{ $labels.id }}"'
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: logstash-docs-nonretry-error-rate
    title: LogstashPipelinePluginsOutputsDocumentsNonRetryableErrorRate
    condition: A
    for: 1h
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: logstash_pipeline_plugins_outputs_documents_non_retryable_failures /
          logstash_pipeline_plugins_outputs_documents_successes > 0.5
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Logstash pipeline output documents non-retryable failures are too high.
      description: 'Logstash pipeline output documents non-retryable failures abnormal
        high

        More than 50% of documents are failed non-retryable

        Instance = {{ $labels.instance }}; Pipeline = {{ $labels.pipeline }}

        Pod = {{ $labels.pod }};  Name = {{ $labels.name }};  id = {{ $labels.id }}"'
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: logstashpipelinefilterfailuretoohigh
    title: LogstashPipelineFilterFailureTooHigh
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: logstash_pipeline_plugins_filters_failures > 100000000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Logstash pipeline filter failures are too high.
      description: 'Logstash pipeline filter failures are too high. Instance = {{
        $labels.instance }}

        Pipeline = {{ $labels.pipeline }}

        Pod = {{ $labels.pod }}; Name = {{ $labels.name }}"'
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: logstasheventspushdurationtoohigh
    title: LogstashEventsPushDurationTooHigh
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: logstash_events_queue_push_duration_in_millis / 1000 > 200000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Logstash Events Push Duration is too high
      description: Logstash Events Push Duration is too high for instance = {{ $labels.instance
        }} pod = {{ $labels.pod }}
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: logstasheventslost
    title: LogstashEventsLost
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: logstash_events_in/logstash_events_out > 1.01
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Logstash Events lost
      description: "Logstash Events are lost increased and more than 1%. Usually we\
        \ have not more than 1.0001 which means <0.01% differ. \n Instance = {{ $labels.instance\
        \ }}\n Pod = {{ $labels.pod }}"
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: logstash-events-processing-insert
    title: LogstashPipelineEventsProcessingTimesInsert
    condition: A
    for: 1m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: logstash_pipeline_events_queue_push_duration_in_millis/logstash_pipeline_events_in
          > 100
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Logstash Events processing time Insert is slow
      description: "Logstash Events processing time Insert is slow, more than 100\
        \ ms\n Pipeline = {{ $labels.pipeline }}\n Instance = {{ $labels.instance\
        \ }} \n Pipeline = {{ $labels.pipeline }} \n Pod = {{ $labels.pod }}"
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: logstash-events-processing-slow
    title: LogstashPipelineEventsProcessingTimesSlow
    condition: A
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:

        editorMode: code
        expr: logstash_pipeline_events_duration_in_millis/logstash_pipeline_events_out
          > 20000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Slow Logstash events processing time output
      description: "Logstash Events processing time Output is slow\n Pipeline = {{\
        \ $labels.pipeline }}\n Instance = {{ $labels.instance }}\n Pipeline = {{\
        \ $labels.pipeline }}\n Pod = {{ $labels.pod }}"
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
