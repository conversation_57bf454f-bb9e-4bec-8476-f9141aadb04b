#!/usr/bin/env python3
"""
convert_prometheus_to_grafana.py

A script to convert a PrometheusRule YAML into GrafanaFolder and GrafanaAlertRuleGroup YAMLs.
Usage:
    python convert_prometheus_to_grafana.py input_prometheus_rule.yaml > output_grafana_crds.yaml
"""

import sys
import yaml

def convert(prom):
    # Extract metadata
    namespace = prom['metadata'].get('namespace', 'default')
    pr_name = prom['metadata']['name']

    # Define GrafanaFolder
    folder_name = f"{namespace}-folder"
    folder = {
        'apiVersion': 'grafana.integreatly.org/v1beta1',
        'kind': 'GrafanaFolder',
        'metadata': {'name': folder_name},
        'spec': {
            'title': namespace,
            'instanceSelector': {
                'matchLabels': {'app': 'grafana'}
            }
        }
    }

    # Define GrafanaAlertRuleGroup
    alert_group = {
        'apiVersion': 'grafana.integreatly.org/v1beta1',
        'kind': 'GrafanaAlertRuleGroup',
        'metadata': {'name': f"{pr_name}-alert-rules"},
        'spec': {
            'folderRef': folder_name,
            'instanceSelector': {'matchLabels': {'app': 'grafana'}},
            'interval': '1m',
            'rules': []
        }
    }

    # Convert each Prometheus rule
    for group in prom['spec'].get('groups', []):
        for rule in group.get('rules', []):
            uid = rule['alert'].lower()
            title = rule['alert']
            expr = rule['expr']
            labels = rule.get('labels', {})
            annotations = rule.get('annotations', {})

            # Build the Grafana query chain: query -> reduce -> threshold
            data = [
                {
                    'refId': 'A',
                    'relativeTimeRange': {'from': 300, 'to': 0},
                    'datasourceUid': 'prometheus',
                    'model': {
                        'datasource': {'type': 'prometheus', 'uid': 'prometheus'},
                        'editorMode': 'code',
                        'expr': expr,
                        'instant': True,
                        'intervalMs': 1000,
                        'legendFormat': '__auto',
                        'maxDataPoints': 43200,
                        'range': False,
                        'refId': 'A'
                    }
                },
                {
                    'refId': 'B',
                    'datasourceUid': '__expr__',
                    'model': {
                        'type': 'reduce',
                        'reducer': {'type': 'last'},
                        'expression': 'A',
                        'intervalMs': 1000,
                        'maxDataPoints': 43200,
                        'refId': 'B'
                    }
                },
                {
                    'refId': 'C',
                    'datasourceUid': '__expr__',
                    'model': {
                        'type': 'threshold',
                        'conditions': [
                            {
                                'evaluator': {'type': 'gt', 'params': [0]},
                                'operator': {'type': 'and'},
                                'query': {'params': ['C']}
                            }
                        ],
                        'intervalMs': 1000,
                        'maxDataPoints': 43200,
                        'refId': 'C'
                    }
                }
            ]

            # Assemble the alert rule
            alert = {
                'uid': uid,
                'title': title,
                'condition': 'C',
                'for': '0s',
                'data': data,
                'labels': labels,
                'annotations': annotations,
                'noDataState': 'NoData',
                'execErrState': 'Error',
                'isPaused': False
            }
            alert_group['spec']['rules'].append(alert)

    return [folder, alert_group]

def main():
    if len(sys.argv) != 2:
        print(f"Usage: {sys.argv[0]} <prometheus_rule.yaml>", file=sys.stderr)
        sys.exit(1)

    with open(sys.argv[1]) as f:
        prom = yaml.safe_load(f)

    docs = convert(prom)
    yaml.safe_dump_all(docs, sys.stdout, sort_keys=False)

if __name__ == '__main__':
    main()
