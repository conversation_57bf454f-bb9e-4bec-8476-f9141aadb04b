#!/usr/bin/env python3
"""
convert_prometheus_to_grafana.py

A script to convert all PrometheusRule YAMLs from prometheus-rules-old directory 
to GrafanaFolder and GrafanaAlertRuleGroup YAMLs in grafana-rules directory.

Usage:
    python convert_prometheus_to_grafana.py
"""

import os
import sys
import yaml
import shutil
from pathlib import Path


def convert_prometheus_rule(prom_rule_content, namespace_override=None):
    """
    Convert a single PrometheusRule to GrafanaFolder and GrafanaAlertRuleGroup.
    
    Args:
        prom_rule_content: Parsed YAML content of PrometheusRule
        namespace_override: Optional namespace override for folder naming
    
    Returns:
        List of Grafana CRDs [folder, alert_group]
    """
    # Extract metadata
    metadata = prom_rule_content.get('metadata', {})
    namespace = namespace_override or metadata.get('namespace', 'default')
    pr_name = metadata.get('name', 'unknown-rule')

    # Define GrafanaFolder
    folder_name = f"{namespace}-folder"
    folder = {
        'apiVersion': 'grafana.integreatly.org/v1beta1',
        'kind': 'GrafanaFolder',
        'metadata': {'name': folder_name},
        'spec': {
            'title': namespace,
            'instanceSelector': {
                'matchLabels': {'app': 'grafana'}
            }
        }
    }

    # Define GrafanaAlertRuleGroup
    alert_group = {
        'apiVersion': 'grafana.integreatly.org/v1beta1',
        'kind': 'GrafanaAlertRuleGroup',
        'metadata': {'name': f"{pr_name}-alert-rules"},
        'spec': {
            'folderRef': folder_name,
            'instanceSelector': {'matchLabels': {'app': 'grafana'}},
            'interval': '1m',
            'rules': []
        }
    }

    # Convert each Prometheus rule
    spec = prom_rule_content.get('spec', {})
    for group in spec.get('groups', []):
        for rule in group.get('rules', []):
            # Skip if not an alert rule
            if 'alert' not in rule:
                continue
                
            alert_name = rule['alert']
            uid = alert_name.lower().replace(' ', '-').replace('_', '-')
            title = alert_name
            expr = rule.get('expr', '')
            labels = rule.get('labels', {})
            annotations = rule.get('annotations', {})
            
            # Parse 'for' duration (default to 0s if not specified)
            for_duration = rule.get('for', '0s')

            # Build the Grafana query chain: query -> reduce -> threshold
            data = [
                {
                    'refId': 'A',
                    'relativeTimeRange': {'from': 300, 'to': 0},
                    'datasourceUid': 'prometheus',
                    'model': {
                        'datasource': {'type': 'prometheus', 'uid': 'prometheus'},
                        'editorMode': 'code',
                        'expr': expr,
                        'instant': True,
                        'intervalMs': 1000,
                        'legendFormat': '__auto',
                        'maxDataPoints': 43200,
                        'range': False,
                        'refId': 'A'
                    }
                },
                {
                    'refId': 'B',
                    'datasourceUid': '__expr__',
                    'model': {
                        'type': 'reduce',
                        'reducer': {'type': 'last'},
                        'expression': 'A',
                        'intervalMs': 1000,
                        'maxDataPoints': 43200,
                        'refId': 'B'
                    }
                },
                {
                    'refId': 'C',
                    'datasourceUid': '__expr__',
                    'model': {
                        'type': 'threshold',
                        'conditions': [
                            {
                                'evaluator': {'type': 'gt', 'params': [0]},
                                'operator': {'type': 'and'},
                                'query': {'params': ['C']}
                            }
                        ],
                        'intervalMs': 1000,
                        'maxDataPoints': 43200,
                        'refId': 'C'
                    }
                }
            ]

            # Assemble the alert rule
            alert = {
                'uid': uid,
                'title': title,
                'condition': 'C',
                'for': for_duration,
                'data': data,
                'labels': labels,
                'annotations': annotations,
                'noDataState': 'NoData',
                'execErrState': 'Error',
                'isPaused': False
            }
            alert_group['spec']['rules'].append(alert)

    return [folder, alert_group]


def process_yaml_file(file_path, output_dir, relative_path):
    """
    Process a single YAML file and convert it to Grafana format.
    
    Args:
        file_path: Path to the input YAML file
        output_dir: Base output directory
        relative_path: Relative path from prometheus-rules-old
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = yaml.safe_load(f)
        
        # Skip if not a PrometheusRule
        if not content or content.get('kind') != 'PrometheusRule':
            print(f"Skipping {file_path} - not a PrometheusRule")
            return
        
        # Extract namespace from directory structure or metadata
        dir_name = Path(relative_path).parent.name
        namespace = dir_name if dir_name != '.' else content.get('metadata', {}).get('namespace', 'default')
        
        # Convert to Grafana format
        grafana_crds = convert_prometheus_rule(content, namespace)
        
        # Create output directory structure
        output_file_dir = output_dir / Path(relative_path).parent
        output_file_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate output filename
        input_filename = Path(file_path).stem
        output_filename = f"{input_filename}-grafana.yaml"
        output_file_path = output_file_dir / output_filename
        
        # Write Grafana CRDs to file
        with open(output_file_path, 'w', encoding='utf-8') as f:
            yaml.safe_dump_all(grafana_crds, f, sort_keys=False, default_flow_style=False)
        
        print(f"Converted: {relative_path} -> {output_file_path.relative_to(output_dir)}")
        
    except Exception as e:
        print(f"Error processing {file_path}: {str(e)}")


def copy_non_yaml_files(src_dir, dst_dir):
    """
    Copy non-YAML files (like README.md, kustomization.yaml) to maintain structure.
    """
    for root, dirs, files in os.walk(src_dir):
        for file in files:
            if not file.endswith(('.yaml', '.yml')) or file == 'kustomization.yaml':
                src_file = Path(root) / file
                rel_path = src_file.relative_to(src_dir)
                dst_file = dst_dir / rel_path
                
                # Create directory if it doesn't exist
                dst_file.parent.mkdir(parents=True, exist_ok=True)
                
                # Copy file
                shutil.copy2(src_file, dst_file)
                print(f"Copied: {rel_path}")


def main():
    """
    Main function to convert all Prometheus rules to Grafana rules.
    """
    # Define directories
    script_dir = Path(__file__).parent
    prometheus_rules_dir = script_dir / "prometheus-rules-old"
    grafana_rules_dir = script_dir / "grafana-rules-new"
    
    # Check if source directory exists
    if not prometheus_rules_dir.exists():
        print(f"Error: Source directory {prometheus_rules_dir} does not exist")
        sys.exit(1)
    
    # Create output directory
    if grafana_rules_dir.exists():
        print(f"Removing existing {grafana_rules_dir}")
        shutil.rmtree(grafana_rules_dir)
    
    grafana_rules_dir.mkdir(exist_ok=True)
    print(f"Created output directory: {grafana_rules_dir}")
    
    # Process all YAML files
    yaml_files_processed = 0
    for root, dirs, files in os.walk(prometheus_rules_dir):
        for file in files:
            if file.endswith(('.yaml', '.yml')) and file != 'kustomization.yaml':
                file_path = Path(root) / file
                relative_path = file_path.relative_to(prometheus_rules_dir)
                
                process_yaml_file(file_path, grafana_rules_dir, relative_path)
                yaml_files_processed += 1
    
    # Copy non-YAML files to maintain structure
    copy_non_yaml_files(prometheus_rules_dir, grafana_rules_dir)
    
    print(f"\nConversion completed!")
    print(f"- Processed {yaml_files_processed} YAML files")
    print(f"- Output directory: {grafana_rules_dir}")
    print(f"- Directory structure maintained")


if __name__ == '__main__':
    main()
