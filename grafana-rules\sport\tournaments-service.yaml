apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: sport-folder
spec:
  allowCrossNamespaceImport: true
  title: sport
  instanceSelector:
    matchLabels:
      dashboards: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: tournaments-service-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: sport-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana
  interval: 1m
  rules:
  - uid: player-tournaments-points-unsuccessful
    title: PlayerTournamentsAggregatedPointsThroughputUnsuccessful
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(increase(player_tournaments_aggregated_points_throughput{job="ews-tournaments-service",
          state="unsuccessful"}[1m])) > 5
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: Sport
      component: Service
    annotations:
      summary: Count of Player tournaments unsuccessful throughput
      description: Tournaments service, number of aggregated points throughput unsuccessful
        is too high
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
