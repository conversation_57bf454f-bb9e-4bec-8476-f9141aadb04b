# Introduction

The following repository contains Prometheus rules for all ( well ... for almost all :) ) important components of the infrastructure. Current Prometheus alerting and recording rules are splited in different directories and are managed as "kustomization" objects. Current system uses **kube-prometheus-stack Helm chart** and every custom rule configuration file is created as a **PrometheusRule** CRD (Custom Resource Definition) resource.

Some learning sources:

- Flux kustomization: https://fluxcd.io/flux/components/kustomize/kustomizations/
- Github kube-prometheus-stack: https://github.com/prometheus-community/helm-charts/tree/main/charts/kube-prometheus-stack
- Artifacthub kube-prometheus-stack: https://artifacthub.io/packages/helm/prometheus-community/kube-prometheus-stack
- Some external documentation: https://docs.syseleven.de/metakube-accelerator/building-blocks/observability-monitoring/kube-prometheus-stack

# Adding a new PrometheusRule configuration

If you want to add new Prometheus recording/alerting rules you need to create (or check if we have already created) directory for the given component and add it in main kustimization.yaml (you can check other directories structure).

After that it is better to follow the main **documentation** regarding how to create a new Prometheus recording/alerting rule:

- https://prometheus.io/docs/practices/alerting/#naming

# Organization convetion

We have some conventions regarding new rule in Prometheus. You need to add two important labels which help to indicate the problem and possible department which is responsible for it:

```
department: <department>
component: <component>
```

If you have any questions feel free to ask in internal DevOps MS Teams channel or your manager.

If the new rule has possible 'runbook' (https://gist.github.com/c4milo/f33158464a65d3c55b46641e4cbe0c2b) you can add it as **annotation** block.

**IMPORTANT**

Before starting work on Prometheus rules get familiar with official documentation:
https://prometheus.io/docs/introduction/overview/
