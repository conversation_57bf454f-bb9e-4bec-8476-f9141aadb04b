apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: barman-folder
spec:
  allowCrossNamespaceImport: true
  title: barman
  instanceSelector:
    matchLabels:
      dashboards: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: barman-rules-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: barman-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana
  interval: 1m
  rules:
  - uid: barmanupbackupfailed
    title: BarmanUpBackupFailed
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        datasource:
          type: prometheus
          uuid: Mimir
        editorMode: code
        expr: barman_up{check="failed_backups"} != 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: barman
      department: DevOps
    annotations:
      summary: Barman Backup Failed
      description: <PERSON><PERSON> has failed to perform a backup job on instance {{ $labels.instance
        }} for server {{ $labels.server }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: barmanbackupfailed
    title: BarmanBackupFailed
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        datasource:
          type: prometheus
          uuid: Mimir
        editorMode: code
        expr: barman_backups_failed > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: barman
      department: DevOps
    annotations:
      summary: Barman Backup Failed
      description: Barman have failed backups {{ $labels.server }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: barmanarchivererrors
    title: BarmanArchiverErrors
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        datasource:
          type: prometheus
          uuid: Mimir
        editorMode: code
        expr: barman_up{check="archiver_errors"} != 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: barman
      department: DevOps
    annotations:
      summary: Barman has archiver errors
      description: BarmanArchiverErrors (instance {{ $labels.instance }})
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: barmanlastbackupold
    title: BarmanLastBackupOld
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        datasource:
          type: prometheus
          uuid: Mimir
        editorMode: code
        expr: (time() - barman_last_backup{server="kas"}) > 3 * 24 * 60 * 60
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: barman
      department: DevOps
    annotations:
      summary: Barman last backup older than 3 days
      description: BarmanLastBackupOld (instance {{ $labels.instance }}, server {{
        $labels.server }})
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: barmanbackupstotal
    title: BarmanBackupsTotal
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        datasource:
          type: prometheus
          uuid: Mimir
        editorMode: code
        expr: barman_backups_total{server != "kas"} < 2
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: barman
      department: DevOps
    annotations:
      summary: BarmanBackupsTotal amount of backups less than 2
      description: Barman has less than 2 postgres backup for server {{ $labels.server
        }}; instance {{ $labels.instance }}
      value: '{{ $value }}'
      runbook_url: https://confluence.egt-digital.com/display/K8S/Barman+backups+missing
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: barmanbackupkaspitchanlow
    title: BarmanBackupKaspitchanLow
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        datasource:
          type: prometheus
          uuid: Mimir
        editorMode: code
        expr: barman_backups_total{server = "kas"} < 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DBA
      component: barman
    annotations:
      summary: Barman KAS missing backup
      description: 'Barman is missing a postgres backup for server {{ $labels.server
        }} (threshold: 1 with current value of {{ $value }})'
      value: '{{ $value }}'
      runbook_url: https://confluence.egt-digital.com/display/K8S/Barman+backups+missing
    noDataState: NoData
    execErrState: Error
    isPaused: false
