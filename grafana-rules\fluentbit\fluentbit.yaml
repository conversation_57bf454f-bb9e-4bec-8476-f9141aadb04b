apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: fluentbit-folder
spec:
  allowCrossNamespaceImport: true
  title: fluentbit
  instanceSelector:
    matchLabels:
      dashboards: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: fluentbit-rules-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: fluentbit-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana
  interval: 1m
  rules:
  - uid: fluentbitlowoutput
    title: FluentbitLowOutput
    condition: A
    for: 15m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: sum(rate(fluentbit_output_proc_bytes_total{namespace="infrastructure"}[15m]))
          by (pod) < 1000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: Kubernetes
      department: DevOps
    annotations:
      summary: Fluentbit low output bytes
      description: 'Fluentbit output bytes are below 1KB for the last 15 minutes for
        pod {{ $labels.pod }}. Currently: {{ $value }}'
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: fluentbithighmemoryusagewarning
    title: FluentbitHighMemoryUsageWarning
    condition: A
    for: 15m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: sum by (pod, node) (container_memory_working_set_bytes{pod=~"fluentbit-fluent-bit-.*"})
          / sum by (pod, node) (kube_pod_container_resource_limits{resource="memory",
          pod=~"fluentbit-fluent-bit-.*"}) > 0.8
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: Kubernetes
      department: DevOps
    annotations:
      summary: Fluentbit warning high memory usage
      description: Fluentbit high memory usage over 80% for the last 15 minutes for
        pod {{ $labels.pod }} located on node {{ $labels.node }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: fluentbithighmemoryusagecritical
    title: FluentbitHighMemoryUsageCritical
    condition: A
    for: 10m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: sum by (pod, node) (container_memory_working_set_bytes{pod=~"fluentbit-fluent-bit-.*"})
          / sum by (pod, node) (kube_pod_container_resource_limits{resource="memory",
          pod=~"fluentbit-fluent-bit-.*"}) > 0.9
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: Kubernetes
      department: DevOps
    annotations:
      summary: Fluentbit critical high memory usage
      description: Fluentbit high memory usage over 90% for the last 10 minutes for
        pod {{ $labels.pod }} located on node {{ $labels.node }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: fluentbithighcpuusagewarning
    title: FluentbitHighCpuUsageWarning
    condition: A
    for: 10m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: node_namespace_pod_container:container_cpu_usage_seconds_total:sum_irate{pod=~"fluentbit-fluent-bit-.*",
          container != ""} > 1.0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: Kubernetes
      department: DevOps
    annotations:
      summary: Fluentbit pod using over 1.0 cpu cores
      description: Fluentbit pod {{ $labels.pod }} located on node {{ $labels.node
        }} is using more than 1.0 cpu cores for the last 10 minutes
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: fluentbithighcpuusagecritical
    title: FluentbitHighCpuUsageCritical
    condition: A
    for: 10m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: node_namespace_pod_container:container_cpu_usage_seconds_total:sum_irate{pod=~"fluentbit-fluent-bit-.*",
          container != ""} > 1.2
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: Kubernetes
      department: DevOps
    annotations:
      summary: Fluentbit pod using over 1.2 cpu cores
      description: Fluentbit pod {{ $labels.pod }} located on node {{ $labels.node
        }} is using more than 1.2 cpu cores for the last 10 minutes
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: fluentbitoutputretriesfailed
    title: FluentbitOutputRetriesFailed
    condition: A
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: irate(fluentbit_output_retries_failed_total[5m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: Kubernetes
      department: DevOps
    annotations:
      summary: Fluentbit failed output retries
      description: Fluentbit instance pod {{ $labels.pod }} on {{ $labels.node }}
        has {{ $value }} failed output retries to {{ $labels.name }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: fluentbitpodcrashlooping
    title: FluentbitPodCrashLooping
    condition: A
    for: 30m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: Mimir
      model:
        editorMode: code
        expr: floor(rate(kube_pod_container_status_restarts_total{job="kube-state-metrics",
          container=~"fluent-bit.*"}[30m]) * 60 * 30) > 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: Kubernetes
      department: DevOps
    annotations:
      summary: Fluentbit container restart
      description: FLuentbit pod container restart rate too high for the last 30 min
        {{ $labels.namespace }}/{{ $labels.pod }} on {{ $labels.node }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
