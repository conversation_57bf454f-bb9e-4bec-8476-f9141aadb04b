apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: redis-folder
spec:
  allowCrossNamespaceImport: true
  title: redis
  instanceSelector:
    matchLabels:
      dashboards: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: redis-rules-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: redis-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana
  interval: 1m
  rules:
  - uid: redisdown
    title: RedisDown
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: redis_up == 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis instance is down
      description: 'Redis down for the Service = {{ $labels.service }} Instance =
        {{ $labels.instance }}) '
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: redistoomanymasters
    title: RedisTooManyMasters
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: count by (service) (redis_instance_info{role="master"}) > 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis too many masters
      description: Redis cluster has too many nodes marked as master for the service
        = {{ $labels.service }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: redismissingmaster
    title: RedisMissingMaster
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: (count by (service) (redis_instance_info{role="master"}) or count by(service)
          (label_replace(vector(0), "service", "test", "service", ".*"))) < 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis missing master
      description: Redis cluster has no node marked as master for the service = {{
        $labels.service }}
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: redisslavedown
    title: RedisSlaveDown
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: count by (service) (redis_instance_info{role="slave"}) < 2
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis missing slave
      description: Redis missing slave for the service = {{ $labels.service }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: redisdisconnectedslaves
    title: RedisDisconnectedSlaves
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: count without (instance, job) (redis_connected_slaves) - sum without
          (instance, job) (redis_connected_slaves) - 1 > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis has disconnected slaves
      description: Redis not replicating for all slaves. Consider reviewing the redis
        replication status. Service = {{ $labels.service }}
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: redisreplicationbroken
    title: RedisReplicationBroken
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: delta(redis_connected_slaves[1m]) < 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis replication broken
      description: Redis instance {{ $labels.instance }} lost a slave for a service
        = {{ $labels.service }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: redisclusterflapping
    title: RedisClusterFlapping
    condition: C
    for: 2m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: changes(redis_connected_slaves[1m]) > 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis cluster flapping
      description: 'Changes have been detected in Redis replica connection for the
        Service = {{ $labels.service }} on Instance = {{ $labels.instance }}

        This can occur when replica nodes lose connection to the master and reconnect
        (a.k.a flapping)'
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: redismissingbackup
    title: RedisMissingBackup
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: time() - redis_rdb_last_save_timestamp_seconds{service!="redis-cache"}
          > 60 * 60 * 24
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis missing backup
      description: 'Redis has not been backed up for 24 hours for the Service = {{
        $labels.service }}

        Instance = {{ $labels.instance }}'
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: redistoomanyconnectionswarning
    title: RedisTooManyConnectionsWarning
    condition: C
    for: 2m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: (redis_connected_clients / redis_config_maxclients) > 0.8
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis too many connections warning
      description: Redis instance has too many connections ( <20% left ) for Service
        = {{ $labels.service }} on Instance = {{ $labels.instance }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: redistoomanyconnectionscritical
    title: RedisTooManyConnectionsCritical
    condition: C
    for: 2m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: (redis_connected_clients / redis_config_maxclients) > 0.9
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis too many connections critical
      description: Redis instance has too many connections ( <10% left ) for Service
        = {{ $labels.service }} on Instance = {{ $labels.instance }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: redisnotenoughconnections
    title: RedisNotEnoughConnections
    condition: C
    for: 2m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: redis_connected_clients < 5
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis not enough connections
      description: Redis instance should have more connections (> 5) Service = {{
        $labels.service }} Instance = {{ $labels.instance }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: redisrejectedconnections
    title: RedisRejectedConnections
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(redis_rejected_connections_total[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis rejected connections
      description: Some connections to Redis has been rejected Service = {{ $labels.service
        }} Instance = {{ $labels.instance }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: redisstreamtoolargedelay
    title: RedisStreamTooLargeDelay
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: consumerGroupMetrics{key="delay" } > 60000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis stream delay is too big
      description: Redis stream  {{ $labels.service }} on Instance {{ $labels.instance
        }} is further than 60s behind on  {{ $labels.streamName  }}. Current value
        is {{ $value }} miliseconds
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: redisdlt
    title: RedisDLT
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: consumerGroupMetrics{key=~"dltlen" } > 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis stream has DLT messages
      description: Redis stream redis-{{ $labels.service }} on Instance {{ $labels.instance
        }} has DLT messages in  {{ $labels.streamName  }}. Current value is {{ $value
        }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: redisaveragereponsetime
    title: RedisAverageReponseTime
    condition: C
    for: 2m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: (sum without(cmd) (rate(redis_commands_duration_seconds_total[2m]))
          /  rate(redis_commands_processed_total[2m]) ) > 0.250
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis high response time
      description: Redis service {{ $labels.service }} on instance {{ $labels.instance  }}
        has  high response time above 250ms
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: redisslavehighlag
    title: RedisSlaveHighLag
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: redis_connected_slave_lag_seconds > 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis Sentinel lag is too big
      description: 'The lag between Redis Sentinel master and replica is longer than
        1s for the last 5 minutes. Service: {{ $labels.service }}; Slave address:
        {{ $labels.slave_ip }} Slave state is: {{ $labels.slave_state }}'
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: redisoutofconfiguredmaxmemory
    title: RedisOutOfConfiguredMaxmemory
    condition: C
    for: 2m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 90 and on (instance)
          redis_memory_max_bytes > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis out of configured maxmemory
      description: Redis is running out of configured maxmemory (> 90%) on instance
        {{ $labels.instance }} for the service {{ $labels.service }}
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: redistoomanyconnectedclients
    title: RedisTooManyConnectedClients
    condition: C
    for: 2m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: redis_connected_clients/redis_config_maxclients > 0.9
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis has too many connected clients
      description: Redis instance has too many connected clients (> 90% from redis_config_maxclients)
        Service = {{ $labels.service }} on Instance = {{ $labels.instance }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: redisexpiredstalepercentagehigh
    title: RedisExpiredStalePercentageHigh
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: redis_expired_stale_percentage > 30
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis expired stale percentage high
      description: Redis instance has more than 30% of expired stale keys Service
        = {{ $labels.service }} on Instance = {{ $labels.instance }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: redislinkdown
    title: RedisLinkDown
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: redis_master_link_up != 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis master link down
      description: Redis master link is down for the Service = {{ $labels.service
        }} on Instance = {{ $labels.instance }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: redisrejectedconnections
    title: RedisRejectedConnections
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(redis_rejected_connections_total[1m]) > 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis rejected connections
      description: Redis rejected connection counter increased for the Service = {{
        $labels.service }} on Instance = {{ $labels.instance }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: redistoomanyblockerclients
    title: RedisTooManyBlockerClients
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: redis_blocked_clients/redis_connected_clients > 0.1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis too many blocked clients
      description: Redis instance has too many blocker clients during 5 minutes (>
        10% of connected clients) Service = {{ $labels.service }} on Instance = {{
        $labels.instance }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: redislowallocatorfragratiowarn
    title: RedisLowAllocatorFragRatioWarn
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: redis_allocator_frag_ratio < 1.0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis memory fragmentation is abnormally low
      description: Redis allocator fragmentation ratio is low (< 100% ) and started
        using swap for service = {{ $labels.service }} on instance = {{ $labels.instance
        }}
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: redishighallocatorfragratiowarn
    title: RedisHighAllocatorFragRatioWarn
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: redis_allocator_frag_ratio > 1.5
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis memory fragmentation is abnormally high
      description: Redis allocator fragmentation ratio is high (> 150% )  Service
        = {{ $labels.service }} on Instance = {{ $labels.instance }}
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: redisallocatorrssratiowarn
    title: RedisAllocatorRssRatioWarn
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: redis_allocator_rss_ratio > 1.5
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis memory fragmentation is abnormally high
      description: Redis Resident Set Size (RSS) ratio is high (>1.5)  Service = {{
        $labels.service }} on Instance = {{ $labels.instance }}
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: frequentaofrewrites
    title: FrequentAOFRewrites
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: redis_aof_rewrite_scheduled > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: AOF rewrite scheduled too frequently
      description: The AOF rewrite process is frequently scheduled for Redis instance
        {{ $labels.instance }} Service = {{ $labels.service }} Investigate AOF file
        size thresholds and system workload.
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: ewssettlementconsumerlagcritical
    title: EWSSettlementConsumerLagCritical
    condition: C
    for: 10m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: max(redis_stream_group_consumer_messages_pending{db="db0", group="betting-service"})
          > 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Settlement Consumer Lag Betting Service
      description: Settlement Consumer Lag is above 1 during last minute period. Current
        value is {{ $value }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: ewsaccountingconsumerlagcritical
    title: EWSAccountingConsumerLagCritical
    condition: C
    for: 10m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: max(redis_stream_group_consumer_messages_pending{db="db0", group="accounting-service",
          stream="strm:transactions"}) > 10
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Accounting Consumer Lag Accounting Service
      description: Accounting Consumer Lag is above 10 during last minute period.
        Current value is {{ $value }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: rediskeyspacemissratiohigh
    title: RedisKeyspaceMissRatioHigh
    condition: C
    for: 30m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: "irate(redis_keyspace_misses_total{service!=\"redis-sport-all\"}[5m])\
          \ /\n(\n  irate(redis_keyspace_misses_total{service!=\"redis-sport-all\"\
          }[5m]) +\n  irate(redis_keyspace_hits_total{service!=\"redis-sport-all\"\
          }[5m])\n) >\n0.5"
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis keyspace miss ratio high
      description: Redis keyspace miss ratio is above 200% for the Service = {{ $labels.service
        }} on Instance = {{ $labels.instance }}; Current ratio = {{ $value }}%
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: redisevictedkeys
    title: RedisEvictedKeys
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: rate(redis_evicted_keys_total[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Redis evicts keys
      description: 'Redis instance {{ $labels.instance }} for service {{ $labels.service
        }} started evict keys. Currently evicted count: {{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
