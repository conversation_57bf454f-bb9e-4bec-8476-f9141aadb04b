apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: nginx-folder
spec:
  allowCrossNamespaceImport: true
  title: nginx
  instanceSelector:
    matchLabels:
      dashboards: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: nginx-plus-rules-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: nginx-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana
  interval: 1m
  rules:
  - uid: nginxplustoomany500srequestswarn
    title: NginxPlusTooMany500sRequestsWarn
    condition: C
    for: 1m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: nginx_ingress_nginxplus_upstream_server_5xx_responses:sum / nginx_ingress_nginxplus_upstream_server_responses:sum
          > 0.50
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Kubernetes
    annotations:
      summary: Nginx 5xx responses ratio warning
      description: 'More than 50% responses with status code 5xx detected for resource
        name: {{ $labels.resource_name }} to service: {{ $labels.exported_service
        }} in namespace: {{ $labels.resource_namespace }}'
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: nginxplustoomany500srequestscritical
    title: NginxPlusTooMany500sRequestsCritical
    condition: C
    for: 1m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: nginx_ingress_nginxplus_upstream_server_5xx_responses:sum / nginx_ingress_nginxplus_upstream_server_responses:sum
          > 0.75
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Kubernetes
    annotations:
      summary: Nginx 5xx responses ratio critical
      description: 'More than 75% responses with status code 5xx detected for resource
        name: {{ $labels.resource_name }} to service: {{ $labels.exported_service
        }} in namespace: {{ $labels.resource_namespace }}'
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: nginxplusuptimehealthwarn
    title: NginxPlusUptimeHealthWarn
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: nginx_ingress_nginxplus_up != 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Kubernetes
    annotations:
      summary: Nginx Plus Health Warning.
      description: "Nginx plus replica down.\n  VALUE = {{ $value }}\n  Pod = {{ $labels.pod\
        \ }}\n  Instance = {{ $labels.instance }})"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: nginxpluscertificatewillexpiresoon
    title: NginxPlusCertificateWillExpireSoon
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: nginx_ingress_controller_ssl_expire_time_seconds < (time() + (30 * 24
          * 3600))
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Kubernetes
    annotations:
      summary: Nginx certificate will expire soon
      description: 'Nginx certificate will expire in less than 30 days on  Host =
        {{ $labels.host }}

        Expiry Date = {{ humanizeTimestamp $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: nginxplusfailedsslhandshakes
    title: NginxPlusFailedSSLHandshakes
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: rate(nginx_ingress_nginxplus_ssl_handshakes_failed[5m]) > 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Kubernetes
    annotations:
      summary: High number of failed SSL handshakes
      description: "Failed SSL handshakes are greater than 1 over a 5-minute period.\n\
        \  VALUE = {{ $value }}\n  Pod = {{ $labels.pod }}\n  Instance = {{ $labels.instance\
        \ }})"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: nginxplushighdroppedconnections
    title: NginxPlusHighDroppedConnections
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(nginx_ingress_nginxplus_connections_dropped[2m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Kubernetes
    annotations:
      summary: High number of dropped connections
      description: "Nginx Plus has dropped connections counter increase 2-minute period.\n\
        \  Current  VALUE = {{ $value }}\n  Pod = {{ $labels.pod }}\n  Instance =\
        \ {{ $labels.instance }})"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: nginxplusupstreamlatencyhigh
    title: NginxPlusUpstreamLatencyHigh
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: nginx_ingress_controller_ingress_upstream_latency_seconds > 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Kubernetes
    annotations:
      summary: High upstream latency
      description: Upstream latency is higher than 1sec over a 5-minute period for
        ingress {{ $labels.ingress }} on host {{ $labels.host }}  LatencySeconds =
        {{ $value }}
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: nginxpluscontrollercheckerror
    title: NginxPlusControllerCheckError
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(nginx_ingress_controller_check_errors[2m]) > 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Kubernetes
    annotations:
      summary: Nginx controller check error
      description: Nginx controller check errors increased over a 2-minute period
        on instance {{ $labels.instance }}, for the ingress {{ $labels.ingress }}
        VALUE = {{ $value }}
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: nginxplusserverzonesdiscardedhigh
    title: NginxPlusServerZonesDiscardedHigh
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: rate(nginx_ingress_nginxplus_server_zone_discarded[5m]) > 10
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Kubernetes
    annotations:
      summary: High number of server zones discarded
      description: 'Server zones discarded are greater than 10 over a 5-minute period
        for server zone {{ $labels.server_zone }}

        Discarded VALUE = {{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: nginxplusupstreamserverfailshigh
    title: NginxPlusUpstreamServerFailsHigh
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: rate(nginx_ingress_nginxplus_upstream_server_fails[5m]) > 2
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Kubernetes
    annotations:
      summary: High number of upstream server fails
      description: "Upstream server fails are greater than 2 over a 5-minute period.\n\
        \ Fails = {{ $value }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: nginxpluszeroproccessnumber
    title: NginxPlusZeroProccessNumber
    condition: C
    for: 2m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: nginx_ingress_controller_nginx_process_num_procs == 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Kubernetes
    annotations:
      summary: Nginx process zero process number
      description: "There no nginx process observer over a 2-minute period.\n ProcNumber\
        \ = {{ $value }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: nginxplusreloaderror
    title: NginxPlusReloadError
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: nginx_ingress_controller_nginx_last_reload_status != 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Kubernetes
    annotations:
      summary: Nginx reload error
      description: "Nginx reload errors increased over a 2-minute period.\n ReloadStatus\
        \ = {{ $value }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: nginxplusupstreamserverstatenotok
    title: NginxPlusUpstreamServerStateNotOk
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: nginx_ingress_nginxplus_upstream_server_state != 1 and nginx_ingress_nginxplus_upstream_server_state
          != 4
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Kubernetes
    annotations:
      summary: Upstream server state not healthy
      description: "Nginx Upstrem server state issue.\nfor the state metric, the string\
        \ values are converted to float64 using the following rule:\n\"up\" -> 1.0,\
        \ \"draining\" -> 2.0, \"down\" -> 3.0, \"unavail\" \u2013> 4.0, \"checking\"\
        \ \u2013> 5.0, \"unhealthy\" -> 6.0.\nState VALUE is equal {{ $value }} on\
        \ Server = {{ $labels.server }} for   Upstream = {{ $labels.upstream }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
