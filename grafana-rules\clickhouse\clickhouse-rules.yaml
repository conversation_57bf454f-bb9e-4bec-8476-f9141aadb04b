apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: clickhouse-folder
spec:
  allowCrossNamespaceImport: true
  title: clickhouse
  instanceSelector:
    matchLabels:
      dashboards: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: prometheus-clickhouse-operator-rules-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: clickhouse-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana
  interval: 1m
  rules:
  - uid: clickhouseprofileeventsrejectedinserts
    title: ClickHouseProfileEventsRejectedInserts
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(ClickHouseProfileEvents_RejectedInserts[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: clickhouse
      department: DBA
    annotations:
      summary: Rejected INSERT queries occurred
      description: 'ClickHouse Profile Events Rejected Inserts is greater than 0 for
        last minute

        new RejectedInserts for last minute = {{ with printf "increase(ClickHouseProfileEvents_RejectedInserts{hostname=''%s''}[1m])"
        .Labels.hostname | query }}{{ . | first | value | printf "%.2f" }} queries{{
        end }}

        `clickhouse-server` have INSERT queries that are rejected due to high number
        of active data parts for partition in a MergeTree, please decrease INSERT
        frequency'
      value: '{{ $value }}'
      runbook_url: https://clickhouse.com/docs/en/development/architecture/#merge-tree
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhousezookeeperhardwareexceptions
    title: ClickHouseZooKeeperHardwareExceptions
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(ClickHouseProfileEvents_ZooKeeperHardwareExceptions[1m]) >
          0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: clickhouse
      department: DBA
    annotations:
      summary: ClickHouse ZooKeeper Hardware Exceptions > 1 in 1 minute
      description: 'new ZooKeeperHardwareExceptions for last minute = {{ with printf
        "increase(ClickHouseProfileEvents_ZooKeeperHardwareExceptions{hostname=''%s''}[1m])"
        .Labels.hostname | query }}{{ . | first | value | printf "%.2f" }} exceptions{{
        end }}

        `clickhouse-server` have unexpected Network errors and similar with communication
        with Zookeeper.

        Clickhouse should reinitialize ZooKeeper session in case of these errors.'
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhousezookeepersession
    title: ClickHouseZooKeeperSession
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: ClickHouseMetrics_ZooKeeperSession > 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: clickhouse
      department: DBA
    annotations:
      summary: ClickHouse ZooKeeper Session > 1
      description: 'ZooKeeperSession = {{ with printf "ClickHouseMetrics_ZooKeeperSession{hostname=''%s''}"
        .Labels.hostname | query }}{{ . | first | value | printf "%.0f" }} sessions{{
        end }}

        Number of sessions (connections) from `clickhouse-server` to `ZooKeeper` shall
        be no more than one,

        because using more than one connection to ZooKeeper may lead to bugs due to
        lack of linearizability (stale reads)

        that ZooKeeper consistency model allows.'
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhousemetrics-distributedfilestoinserthight
    title: ClickHouseMetrics_DistributedFilesToInsertHight
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: ClickHouseMetrics_DistributedFilesToInsert > 50
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: clickhouse
      department: DBA
    annotations:
      summary: ClickHouse Distributed Files To Insert Hight > 50
      description: 'DistributedFilesToInsert = {{ with printf "ClickHouseMetrics_DistributedFilesToInsert{instance=''%s''}"
        .Labels.instance | query }}{{ . | first | value }} files{{ end }}

        `clickhouse-server` have too much files which not insert to `*MergeTree` tables
        via `Distributed` table engine

        Check not synced .bin files /var/lib/clickhouse/data/*/*/*/*.bin`` ClickHouse
        Distributed Files To Insert is greater than 50'
      value: '{{ $value }}'
      runbook_url: https://clickhouse.tech/docs/en/operations/table_engines/distributed/
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhousetoomanyrunningqueries
    title: ClickHouseTooManyRunningQueries
    condition: C
    for: 10m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: irate(ClickHouseProfileEvents_Query[1m]) > 50
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: clickhouse
      department: DBA
    annotations:
      summary: Too much running queries.
      description: 'Counter increased for last 10 mins. Please analyze your workload.

        Each concurrent SELECT query use memory in JOINs use CPU for running aggregation
        function and can read lot of data from disk when scan parts in partitions
        and utilize disk I/O.

        Each concurrent INSERT query, allocate around 1MB per each column in an inserted
        table and utilize disk I/O.

        Look at following documentation parts:

        - https://clickhouse.com/docs/en/operations/settings/query-complexity/

        - https://clickhouse.com/docs/en/operations/quotas/

        - https://clickhouse.com/docs/en/operations/server-configuration-parameters/settings/#max-concurrent-queries'
      runbook_url: https://clickhouse.com/docs/en/operations/system-tables/query_log/
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhouseserverrestartrecently
    title: ClickHouseServerRestartRecently
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: ClickHouseAsyncMetrics_Uptime > 1 < 180
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: clickhouse
      department: DBA
    annotations:
      summary: ClickHouse Server Started Recently
      description: ClickHouse server has restarted recently. Process has been start
        less than 3 minutes ago.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhousedistributedfilestoinserthigh
    title: ClickHouseDistributedFilesToInsertHigh
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: ClickHouseMetrics_DistributedFilesToInsert > 50
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: clickhouse
      department: DBA
    annotations:
      summary: Clickhouse Server have Distributed Files to Insert > 50
      description: 'DistributedFilesToInsert = {{ with printf "ClickHouseMetrics_DistributedFilesToInsert{hostname=''%s''}"
        .Labels.hostname | query }}{{ . | first | value }} files{{ end }}

        `clickhouse-server` have too much files which not insert to `*MergeTree` tables
        via `Distributed` table engine

        Also, check documentation:

        https://clickhouse.com/docs/en/engines/table-engines/special/distributed/

        When you insert data to `Distributed` table.

        Data is written to target `*MergreTree` tables asynchronously.

        When inserted in the table, the data block is just written to the local file
        system.

        The data is sent to the remote servers in the background as soon as possible.

        The period for sending data is managed by the `distributed_directory_monitor_sleep_time_ms`
        and `distributed_directory_monitor_max_sleep_time_ms` settings.

        The Distributed engine sends each file with inserted data separately, but
        you can enable batch sending of files with the `distributed_directory_monitor_batch_inserts`
        setting

        Also, you can manage distributed tables:

        https://clickhouse.com/docs/en/sql-reference/statements/system/#managing-distributed-tables'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhouse-distributed-conn-exc
    title: ClickHouseDistributedConnectionExceptions
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(ClickHouseProfileEvents_DistributedConnectionFailTry[1m]) >
          0 or increase(ClickHouseProfileEvents_DistributedConnectionFailAtAll[1m])
          > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: clickhouse
      department: DBA
    annotations:
      summary: Distributed connections fails occurred
      description: 'DistributedConnectionFailTry[for last minute] = {{ with printf
        "increase(ClickHouseProfileEvents_DistributedConnectionFailTry{hostname=''%s''}[1m])"
        .Labels.hostname  | query }}{{ . | first | value | printf "%.2f" }} errors{{
        end }}

        DistributedConnectionFailAtAll[for last minute]) = {{ with printf "increase(ClickHouseProfileEvents_DistributedConnectionFailAtAll{hostname=''%s''}[1m])"
        .Labels.hostname  | query }}{{ . | first | value | printf "%.2f" }} errors{{
        end }}

        Please, check communications between clickhouse server and host `remote_servers`
        in `/etc/clickhouse-server/`'
      runbook_url: https://clickhouse.com/docs/en/operations/server-configuration-parameters/settings/#server-settings-remote-servers
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhousereadonlyreplica
    title: ClickHouseReadonlyReplica
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: ClickHouseMetrics_ReadonlyReplica > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: high
      component: clickhouse
      department: DBA
    annotations:
      summary: ReadOnly replica occurred
      description: '`ClickHouseMetrics_ReadonlyReplica` = {{ with printf "ClickHouseMetrics_ReadonlyReplica{hostname=''%s''}"
        .Labels.hostname | query }}{{ . | first | value }} replicas{{ end }}

        `clickhouse-server` have ReplicatedMergeTree tables that are currently in
        readonly state due to re-initialization after ZooKeeper session loss or due
        to startup without ZooKeeper configured.

        Please check following things:

        - nodes have free enough RAM and Disk

        - status of clickhouse-server processes, logts

        - connection between clickhouse-server zookeeper'
      runbbok_url: https://clickhouse.com/docs/en/engines/table-engines/mergetree-family/replication/#recovery-after-failures
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhousememoryusagecritical
    title: ClickhouseMemoryUsageCritical
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: ClickHouseAsyncMetrics_CGroupMemoryUsed / ClickHouseAsyncMetrics_CGroupMemoryTotal
          * 100 > 90
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: clickhouse
      department: DBA
    annotations:
      summary: ClickHouse Memory Usage Critical.
      description: "Memory usage is critically high, over 90%.\n  VALUE = {{ $value\
        \ }}\n  Service = {{ $labels.service }}\n  Instance =  {{ $labels.instance\
        \ }})"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhousememoryusagewarning
    title: ClickhouseMemoryUsageWarning
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: ClickHouseAsyncMetrics_CGroupMemoryUsed / ClickHouseAsyncMetrics_CGroupMemoryTotal
          * 100 > 80
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: clickhouse
      department: DBA
    annotations:
      summary: ClickHouse Memory Usage Warning
      description: "Memory usage is over 80%.\n  VALUE = {{ $value }}\n  Instance\
        \ =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhousehighnetworktraffic
    title: ClickhouseHighNetworkTraffic
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: ClickHouseMetrics_NetworkSend > 10 or ClickHouseMetrics_NetworkReceive
          > 10
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: clickhouse
      department: DBA
    annotations:
      summary: ClickHouse High Network Traffic
      description: "Network traffic is unusually high, may affect cluster performance.\n\
        \  VALUE = {{ $value }}\n  Instance =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhousehightcpconnections
    title: ClickhouseHighTcpConnections
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: ClickHouseMetrics_TCPConnection > 10
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: clickhouse
      department: DBA
    annotations:
      summary: ClickHouse High TCP Connections
      description: "High number of TCP connections, indicating heavy client or inter-cluster\
        \ communication.\n  VALUE = {{ $value }}\n  Instance =  {{ $labels.instance\
        \ }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhouseinterserverconnectionissues
    title: ClickhouseInterserverConnectionIssues
    condition: C
    for: 1m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(ClickHouseMetrics_InterserverConnection[5m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: clickhouse
      department: DBA
    annotations:
      summary: ClickHouse Interserver Connection Issues
      description: "An increase in interserver connections may indicate replication\
        \ or distributed query handling issues.\n  VALUE = {{ $value }}\n  Instance\
        \ =  {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhousenetworkerrors
    title: ClickHouseNetworkErrors
    condition: C
    for: 10m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(ClickHouseErrorMetric_DNS_ERROR[1m]) > 0 or increase(ClickHouseErrorMetric_NETWORK_ERROR[1m])
          > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: clickhouse
      department: DBA
    annotations:
      summary: ClickHouse DNS/Network Errors Occured
      description: 'ClickHouse DNS_ERROR or NETWORK_ERROR increased for the last minute
        and is greater than 0 since last server restart

        DNS_ERROR[1m] = {{ with printf "increase(ClickHouseErrorMetric_DNS_ERROR{hostname=''%s''}[1m])"
        .Labels.hostname | query }}{{ . | first | value }} errors{{ end }}

        NETWORK_ERROR[1m] = {{ with printf "increase(ClickHouseErrorMetric_NETWORK_ERROR{hostname=''%s''}[1m])"
        .Labels.hostname | query }}{{ . | first | value  }} errors{{ end }}

        Please check DNS settings in `/etc/resolve.conf` and `<remote_servers>` part
        of `/etc/clickhouse-server/`'
      value: '{{ $value }}'
      runbook_url: https://clickhouse.com/docs/en/guides/troubleshooting#check-the-logs
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhouseerrormetricok
    title: ClickHouseErrorMetricOK
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(ClickHouseErrorMetric_OK[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: wargning
      component: clickhouse
      department: DBA
    annotations:
      summary: ClickHouse Error Metric OK is not ok now.
      description: ClickHouse_Error_Metric_OK - The number of errors increased for
        the last minute
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhouse-cannot-allocate-mem
    title: ClickHouseErrorMetricCannotAllocateMemory
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(ClickHouseErrorMetric_CANNOT_ALLOCATE_MEMORY[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: clickhouse
      department: DBA
    annotations:
      summary: ClickHouse Error Metric - cannot allocate memory
      description: ClickHouse Error Metric CANNOT_ALLOCATE_MEMORY increased for the
        last minute. Check the memory utilization.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhouseerrormetricipaddressnotallowed
    title: ClickHouseErrorMetricIPAddressNotAllowed
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(ClickHouseErrorMetric_IP_ADDRESS_NOT_ALLOWED[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: clickhouse
      department: DBA
    annotations:
      summary: ClickHouse Error Metric - ip address not allowed
      description: ClickHouse Error Metric IP_ADDRESS_NOT_ALLOWED increased for the
        last minute. Check logs, configs and firewall rules.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhouse-too-many-requests
    title: ClickHouseErrorMetricReceivedErrorTooManyRequests
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(ClickHouseErrorMetric_RECEIVED_ERROR_TOO_MANY_REQUESTS[1m])>
          0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: clickhouse
      department: DBA
    annotations:
      summary: ClickHouse Error Metric - received error too many requests
      description: ClickHouse Error Metric RECEIVED_ERROR_TOO_MANY_REQUESTS increased
        for the last minute. Verify the connections and requests.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhouse-db-replication-failed
    title: ClickHouseErrorMetricDatabaseReplicationFailed
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(ClickHouseErrorMetric_DATABASE_REPLICATION_FAILED[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: clickhouse
      department: DBA
    annotations:
      summary: ClickHouse Error Metric - database replication failed
      description: ClickHouse database replication failed. Check the replication status.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhouseerrormetricstdexception
    title: ClickHouseErrorMetricStdException
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(ClickHouseErrorMetric_STD_EXCEPTION[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: clickhouse
      department: DBA
    annotations:
      summary: ClickHouse Error Metric - STD exception
      description: ClickHouse Error Metric STD_EXCEPTION increased for the last minute.
        Check logs.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhouseerrormetricqueryistoolarge
    title: ClickHouseErrorMetricQueryIsTooLarge
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(ClickHouseErrorMetric_QUERY_IS_TOO_LARGE[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: clickhouse
      department: DBA
    annotations:
      summary: ClickHouse Error Metric -  QUERY_IS_TOO_LARGE
      description: TOO Large QUERY appeared in last minute.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhouse-too-many-queries
    title: ClickHouseErrorMetricTooManySimultaneousQueries
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(ClickHouseErrorMetric_TOO_MANY_SIMULTANEOUS_QUERIES[1m]) >
          0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: clickhouse
      department: DBA
    annotations:
      summary: ClickHouse Error Metric TOO_MANY_SIMULTANEOUS_QUERIES
      description: ClickHouse_Error_Metric_TOO_MANY_SIMULTANEOUS_QUERIES increased
        for the last minute
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhouseerrormetricnofreeconnection
    title: ClickHouseErrorMetricNoFreeConnection
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(ClickHouseErrorMetric_NO_FREE_CONNECTION[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: clickhouse
      department: DBA
    annotations:
      summary: ClickHouse Error Metric NO_FREE_CONNECTION
      description: ClickHouse_Error_Metric_NO_FREE_CONNECTION increased for the last
        minute
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhouseerrormetricnozookeeper
    title: ClickHouseErrorMetricNoZookeeper
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(ClickHouseErrorMetric_NO_ZOOKEEPER[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: clickhouse
      department: DBA
    annotations:
      summary: ClickHouse Error Metric NO_ZOOKEEPER
      description: ClickHouse_Error_Metric_NO_ZOOKEEPER increased for the last minute
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhouseerrormetricmemorylimitexceeded
    title: ClickHouseErrorMetricMemoryLimitExceeded
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(ClickHouseErrorMetric_MEMORY_LIMIT_EXCEEDED[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: clickhouse
      department: DBA
    annotations:
      summary: ClickHouse Error Metric MEMORY_LIMIT_EXCEEDED
      description: ClickHouse_Error_Metric_MEMORY_LIMIT_EXCEEDED increased for the
        last minute
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhouseerrormetricnoactivereplicas
    title: ClickHouseErrorMetricNoActiveReplicas
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(ClickHouseErrorMetric_NO_ACTIVE_REPLICAS[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      component: clickhouse
      department: DBA
    annotations:
      summary: ClickHouse Error Metric NO_ACTIVE_REPLICAS
      description: ClickHouse_Error_Metric_NO_ACTIVE_REPLICAS increased for the last
        minute
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhouseaccessdeniederrors
    title: ClickhouseAccessDeniedErrors
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(ClickHouseErrorMetric_RESOURCE_ACCESS_DENIED[5m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: info
      component: clickhouse
      department: DBA
    annotations:
      summary: ClickHouse Access Denied Errors for last 5 minutes increased
      description: "Access denied errors have been logged, which could indicate permission\
        \ issues or unauthorized access attempts.\n  Errors = {{ $value }}\n  instance\
        \ = {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhousereplicaerrors
    title: ClickhouseReplicaErrors
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: ClickHouseErrorMetric_ALL_REPLICAS_ARE_STALE == 1 or ClickHouseErrorMetric_ALL_REPLICAS_LOST
          == 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: clickhouse
      department: DBA
    annotations:
      summary: ClickHouse Replica Errors
      description: "Critical replica errors detected, either all replicas are stale\
        \ or lost.\n  VALUE = {{ $value }}\n  instance = {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhousenoavailablereplicas
    title: ClickhouseNoAvailableReplicas
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: ClickHouseErrorMetric_NO_AVAILABLE_REPLICA == 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: clickhouse
      department: DBA
    annotations:
      summary: ClickHouse No Available Replicas
      description: "No available replicas in ClickHouse.\n  VALUE = {{ $value }}\n\
        \  instance = {{ $labels.instance }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clickhousenolivereplicas
    title: ClickhouseNoLiveReplicas
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: ClickHouseErrorMetric_TOO_FEW_LIVE_REPLICAS == 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      component: clickhouse
      department: DBA
    annotations:
      summary: ClickHouse No Live Replicas
      description: "There are too few live replicas available, risking data loss and\
        \ service disruption.\n  VALUE = {{ $value }}\n  instance = {{ $labels.instance\
        \ }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
