apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: regulatory-folder
spec:
  allowCrossNamespaceImport: true
  title: regulatory
  instanceSelector:
    matchLabels:
      dashboards: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: postgres-nra-checks-rules-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: regulatory-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana
  interval: 1m
  rules:
  - uid: nraservicefailuresduringsending
    title: NRAServiceFailuresDuringSending
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: rate(NRA_Service_failures_during_sending{job="postgres-nra-checks"}[5m])
          > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Regulatory
    annotations:
      summary: NRA Service failures during sending
      description: There are NRA Service Failures During Sending
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: nraservicefailures
    title: NRAServiceFailures
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(NRA_Service_failures_for_the_last_hour{job="postgres-nra-checks"})
          > 1000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Regulatory
    annotations:
      summary: NRA Service Failures
      description: There are too many NRA Service failures for the Last hour for ALL
        Business units.
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: nraduplicatedsequencenumberfield
    title: NRADuplicatedSequenceNumberField
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: NRA_Duplicated_Sequence_number_field > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: crm
      component: Regulatory
    annotations:
      summary: Failures of unexpected type in NAP reporting
      description: There are unexpected NAP responses for the last day for {{ $labels.Business_Unit
        }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
