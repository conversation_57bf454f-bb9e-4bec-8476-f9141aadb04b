@echo off
echo Adding allowCrossNamespaceImport: true to all YAML files...

REM Process each YAML file and add allowCrossNamespaceImport: true after spec: if not already present
powershell -Command "& { $files = Get-ChildItem -Path 'grafana-rules' -Recurse -Filter '*.yaml'; foreach ($file in $files) { $content = Get-Content $file.FullName; $newContent = @(); $i = 0; while ($i -lt $content.Length) { $newContent += $content[$i]; if ($content[$i] -match '^spec:$' -and $i+1 -lt $content.Length -and $content[$i+1] -notmatch 'allowCrossNamespaceImport') { $newContent += '  allowCrossNamespaceImport: true'; } $i++; } Set-Content -Path $file.FullName -Value $newContent; } }"

echo Done! allowCrossNamespaceImport: true has been added to all YAML files.
pause
