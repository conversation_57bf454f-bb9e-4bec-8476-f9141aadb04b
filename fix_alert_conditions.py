#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix Grafana alert conditions to use default behavior.
Changes all alerts to use condition: A and ensures consistent datasource configuration.
"""

import os
import yaml
import glob
from typing import Dict, List, Any
import argparse


def fix_alert_rule(rule: Dict[str, Any]) -> bool:
    """Fix a single alert rule to use default behavior. Returns True if changes were made."""
    changes_made = False
    
    # Fix condition to use A (default behavior)
    if rule.get('condition') != 'A':
        rule['condition'] = 'A'
        changes_made = True
        print(f"  Fixed condition for rule: {rule.get('uid', 'unknown')}")
    
    # Fix data blocks
    data_blocks = rule.get('data', [])
    if isinstance(data_blocks, list) and len(data_blocks) > 0:
        # Keep only the first data block (refId: A)
        first_block = data_blocks[0]
        if len(data_blocks) > 1:
            rule['data'] = [first_block]
            changes_made = True
            print(f"  Removed extra data blocks for rule: {rule.get('uid', 'unknown')}")
        
        # Fix datasource configuration in the first block
        if 'model' in first_block and 'datasource' in first_block['model']:
            datasource = first_block['model']['datasource']
            
            # Ensure datasource has both type and uid
            if isinstance(datasource, dict):
                if 'type' not in datasource:
                    datasource['type'] = 'prometheus'
                    changes_made = True
                
                if 'uid' not in datasource:
                    # Use the datasourceUid if available, otherwise default to 'prometheus'
                    uid_value = first_block.get('datasourceUid', 'prometheus')
                    datasource['uid'] = uid_value
                    changes_made = True
        
        # Ensure datasourceUid is set at the block level
        if 'datasourceUid' not in first_block:
            first_block['datasourceUid'] = 'prometheus'
            changes_made = True
        
        # Fix commented out datasourceUid
        if first_block.get('datasourceUid') is None:
            first_block['datasourceUid'] = 'prometheus'
            changes_made = True
    
    return changes_made


def process_yaml_content(content: Dict[str, Any]) -> bool:
    """Process YAML content and fix alert rules. Returns True if changes were made."""
    changes_made = False
    
    def process_recursive(obj):
        nonlocal changes_made
        
        if isinstance(obj, dict):
            # Check if this is a GrafanaAlertRuleGroup
            if obj.get('kind') == 'GrafanaAlertRuleGroup':
                spec = obj.get('spec', {})
                rules = spec.get('rules', [])
                
                for rule in rules:
                    if fix_alert_rule(rule):
                        changes_made = True
            
            # Recursively process all values in the dict
            for value in obj.values():
                process_recursive(value)
        
        elif isinstance(obj, list):
            # Recursively process all items in the list
            for item in obj:
                process_recursive(item)
    
    process_recursive(content)
    return changes_made


def process_yaml_file(file_path: str, dry_run: bool = False) -> bool:
    """Process a single YAML file. Returns True if changes were made."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Handle multiple YAML documents in one file
        documents = list(yaml.safe_load_all(content))
        
        changes_made = False
        for doc in documents:
            if doc is not None:
                if process_yaml_content(doc):
                    changes_made = True
        
        if changes_made:
            print(f"Changes made to: {file_path}")
            
            if not dry_run:
                # Write back the modified content
                with open(file_path, 'w', encoding='utf-8') as f:
                    yaml.dump_all(documents, f, default_flow_style=False, sort_keys=False)
                print(f"  File updated successfully")
            else:
                print(f"  [DRY RUN] Would update file")
        
        return changes_made
    
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False


def find_yaml_files(root_dir: str) -> List[str]:
    """Find all YAML files in the directory tree."""
    yaml_files = []
    
    # Use glob to find all .yaml and .yml files recursively
    patterns = ['**/*.yaml', '**/*.yml']
    
    for pattern in patterns:
        yaml_files.extend(glob.glob(os.path.join(root_dir, pattern), recursive=True))
    
    return sorted(yaml_files)


def main():
    parser = argparse.ArgumentParser(description='Fix Grafana alert conditions to use default behavior')
    parser.add_argument('--root-dir', default='.', help='Root directory to search for YAML files')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be changed without making changes')
    parser.add_argument('--file', help='Process a specific file instead of searching directory')
    
    args = parser.parse_args()
    
    if args.file:
        yaml_files = [args.file]
        print(f"Processing specific file: {args.file}")
    else:
        root_dir = os.path.abspath(args.root_dir)
        print(f"Searching for YAML files in: {root_dir}")
        yaml_files = find_yaml_files(root_dir)
        print(f"Found {len(yaml_files)} YAML files")
    
    if args.dry_run:
        print("DRY RUN MODE - No files will be modified")
    
    total_changed = 0
    
    for yaml_file in yaml_files:
        print(f"\nProcessing: {yaml_file}")
        
        if process_yaml_file(yaml_file, args.dry_run):
            total_changed += 1
        else:
            print(f"  No changes needed")
    
    print(f"\n=== Summary ===")
    print(f"Total files processed: {len(yaml_files)}")
    print(f"Files with changes: {total_changed}")
    
    if args.dry_run:
        print("Run without --dry-run to apply changes")


if __name__ == '__main__':
    main()
