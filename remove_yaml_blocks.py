#!/usr/bin/env python3
"""
<PERSON>ript to remove specific YAML blocks from all YAML files in the repository.
Removes blocks with refId: B and refId: C that match specific patterns.
"""

import os
import yaml
import glob
from typing import Dict, List, Any
import argparse


def is_target_block_b(block: Dict[str, Any]) -> bool:
    """Check if a block matches the target refId: B pattern to remove."""
    if not isinstance(block, dict):
        return False
    
    return (
        block.get('refId') == 'B' and
        block.get('datasourceUid') == '__expr__' and
        isinstance(block.get('model'), dict) and
        block['model'].get('type') == 'reduce' and
        isinstance(block['model'].get('reducer'), dict) and
        block['model']['reducer'].get('type') == 'last' and
        block['model'].get('expression') == 'A' and
        block['model'].get('intervalMs') == 1000 and
        block['model'].get('maxDataPoints') == 43200 and
        block['model'].get('refId') == 'B'
    )


def is_target_block_c(block: Dict[str, Any]) -> bool:
    """Check if a block matches the target refId: C pattern to remove."""
    if not isinstance(block, dict):
        return False
    
    model = block.get('model', {})
    if not isinstance(model, dict):
        return False
    
    conditions = model.get('conditions', [])
    if not isinstance(conditions, list) or len(conditions) != 1:
        return False
    
    condition = conditions[0]
    if not isinstance(condition, dict):
        return False
    
    evaluator = condition.get('evaluator', {})
    operator = condition.get('operator', {})
    query = condition.get('query', {})
    
    return (
        block.get('refId') == 'C' and
        block.get('datasourceUid') == '__expr__' and
        model.get('type') == 'threshold' and
        isinstance(evaluator, dict) and
        evaluator.get('type') == 'gt' and
        evaluator.get('params') == [0] and
        isinstance(operator, dict) and
        operator.get('type') == 'and' and
        isinstance(query, dict) and
        query.get('params') == ['C'] and
        model.get('intervalMs') == 1000 and
        model.get('maxDataPoints') == 43200 and
        model.get('refId') == 'C'
    )


def remove_target_blocks_from_data_list(data_list: List[Any]) -> List[Any]:
    """Remove target blocks from a list of data blocks."""
    if not isinstance(data_list, list):
        return data_list
    
    filtered_data = []
    for block in data_list:
        if not (is_target_block_b(block) or is_target_block_c(block)):
            filtered_data.append(block)
        else:
            print(f"  Removing block with refId: {block.get('refId')}")
    
    return filtered_data


def process_yaml_content(content: Dict[str, Any]) -> bool:
    """Process YAML content and remove target blocks. Returns True if changes were made."""
    changes_made = False
    
    def process_recursive(obj):
        nonlocal changes_made
        
        if isinstance(obj, dict):
            # Check if this dict has a 'data' key with a list
            if 'data' in obj and isinstance(obj['data'], list):
                original_length = len(obj['data'])
                obj['data'] = remove_target_blocks_from_data_list(obj['data'])
                if len(obj['data']) != original_length:
                    changes_made = True
            
            # Recursively process all values in the dict
            for value in obj.values():
                process_recursive(value)
        
        elif isinstance(obj, list):
            # Recursively process all items in the list
            for item in obj:
                process_recursive(item)
    
    process_recursive(content)
    return changes_made


def process_yaml_file(file_path: str, dry_run: bool = False) -> bool:
    """Process a single YAML file. Returns True if changes were made."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Handle multiple YAML documents in one file
        documents = list(yaml.safe_load_all(content))
        
        changes_made = False
        for doc in documents:
            if doc is not None:
                if process_yaml_content(doc):
                    changes_made = True
        
        if changes_made:
            print(f"Changes made to: {file_path}")
            
            if not dry_run:
                # Write back the modified content
                with open(file_path, 'w', encoding='utf-8') as f:
                    yaml.dump_all(documents, f, default_flow_style=False, sort_keys=False)
                print(f"  File updated successfully")
            else:
                print(f"  [DRY RUN] Would update file")
        
        return changes_made
    
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False


def find_yaml_files(root_dir: str) -> List[str]:
    """Find all YAML files in the directory tree."""
    yaml_files = []
    
    # Use glob to find all .yaml and .yml files recursively
    patterns = ['**/*.yaml', '**/*.yml']
    
    for pattern in patterns:
        yaml_files.extend(glob.glob(os.path.join(root_dir, pattern), recursive=True))
    
    return sorted(yaml_files)


def main():
    parser = argparse.ArgumentParser(description='Remove specific YAML blocks from all YAML files')
    parser.add_argument('--root-dir', default='.', help='Root directory to search for YAML files')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be changed without making changes')
    
    args = parser.parse_args()
    
    root_dir = os.path.abspath(args.root_dir)
    print(f"Searching for YAML files in: {root_dir}")
    
    yaml_files = find_yaml_files(root_dir)
    print(f"Found {len(yaml_files)} YAML files")
    
    if args.dry_run:
        print("DRY RUN MODE - No files will be modified")
    
    total_changed = 0
    
    for yaml_file in yaml_files:
        print(f"\nProcessing: {yaml_file}")
        
        if process_yaml_file(yaml_file, args.dry_run):
            total_changed += 1
        else:
            print(f"  No changes needed")
    
    print(f"\n=== Summary ===")
    print(f"Total files processed: {len(yaml_files)}")
    print(f"Files with changes: {total_changed}")
    
    if args.dry_run:
        print("Run without --dry-run to apply changes")


if __name__ == '__main__':
    main()
