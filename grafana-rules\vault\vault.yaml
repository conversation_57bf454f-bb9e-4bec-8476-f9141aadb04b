apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: vault-folder
spec:
  allowCrossNamespaceImport: true
  title: vault
  instanceSelector:
    matchLabels:
      dashboards: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: vault-rules-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: vault-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana
  interval: 1m
  rules:
  - uid: vaultsealed
    title: VaultSealed
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: vault_core_unsealed == 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Vault sealed
      description: "Vault instance is sealed on instance: {{ $labels.instance }}\n\
        \  VALUE = {{ $value }}\n  cluster = {{ $labels.cluster }}"
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: vaulttoomanypendingtokens
    title: VaultTooManyPendingTokens
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: avg(vault_token_create_count - vault_token_store_count) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Vault too many pending tokens
      description: "Too many pending tokens; instance: {{ $labels.instance }}: {{\
        \ $value | printf \"%.2f\"}}%\n  VALUE = {{ $value }}"
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: vaultclusterhealth
    title: VaultClusterHealth
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(vault_core_active) / count(vault_core_active) <= 0.5
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Vault cluster health is not OK
      description: 'Vault cluster is not healthy; vault_core_active: {{ $value | printf
        "%.2f"}}%'
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: vaultsuspicioushighmemoryusage
    title: VaultSuspiciousHighMemoryUsage
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(vault_runtime_alloc_bytes) / count(vault_runtime_alloc_bytes) >
          5e+8
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Vault suspicious high memory usage
      description: 'Vault suspicious high memory usage;

        instance = {{ $labels.instance }};

        vault_runtime_alloc_bytes = {{ $value }}

        Usually we have usage <50M'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: vaultauditlogresponcefailure
    title: VaultAuditLogResponceFailure
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: vault_audit_log_response_failure > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Vault audit log response failure
      description: 'Vault audit log response failure; instance: {{ $labels.instance
        }};

        FailureValue = {{ $value }}

        Service = {{ $labels.service }}

        Namespace = {{ $labels.namespace }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: vaultauditlogrequestfailure
    title: VaultAuditLogRequestFailure
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: vault_audit_log_request_failure > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: Infrastructure
    annotations:
      summary: Vault audit log request failure
      description: 'Vault audit log request failure; instance: {{ $labels.instance
        }};

        FailureValue = {{ $value }}

        Service = {{ $labels.service }}

        Namespace = {{ $labels.namespace }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
