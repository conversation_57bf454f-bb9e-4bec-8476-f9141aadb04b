apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaFolder
metadata:
  name: kafka-folder
spec:
  allowCrossNamespaceImport: true
  title: kafka
  instanceSelector:
    matchLabels:
      dashboards: grafana
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaAlertRuleGroup
metadata:
  name: prometheus-k8s-kafka-rules-alert-rules
spec:
  allowCrossNamespaceImport: true
  folderRef: kafka-folder
  instanceSelector:
    matchLabels:
      dashboards: grafana
  interval: 1m
  rules:
  - uid: kafkarunningoutofdiskspace
    title: KafkaRunningOutOfDiskSpace
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: kubelet_volume_stats_available_bytes{persistentvolumeclaim=~"data-.*-kafka-[0-9]"}
          / kubelet_volume_stats_capacity_bytes{persistentvolumeclaim=~"data-.*-kafka-[0-9]"}
          * 100 < 30
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Kafka disk running out of space warning
      description: Kafka disk running out of space for instance {{ $labels.instance
        }} currently using {{ $value }}% of allocated space
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: predictionkafkarunningoutofdiskspace
    title: PredictionKafkaRunningOutOfDiskSpace
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: predict_linear(kubelet_volume_stats_available_bytes{persistentvolumeclaim=~"data-.*-kafka-[0-9]"}[1h],4*3600)
          / kubelet_volume_stats_capacity_bytes{persistentvolumeclaim=~"data-.*-kafka-[0-9]"}
          * 100  < 20
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: PREDICTION Warning Kafka disk running out of space in 4 hours.
      description: PREDICTION Kafka disk running out of space in 4 hours for instance
        {{ $labels.instance }} currently using {{ $value }}% of allocated space
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: underreplicatedpartitions
    title: UnderReplicatedPartitions
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: kafka_server_replicamanager_underreplicatedpartitions > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Kafka under replicated partitions
      description: There are {{ $value }} under replicated partitions on {{ $labels.kubernetes_pod_name
        }} located on {{ $labels.node_name }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: abnormalcontrollerstate
    title: AbnormalControllerState
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(kafka_controller_kafkacontroller_activecontrollercount) by (namespace,
          strimzi_io_controller_name) != 1
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: infrastructure
    annotations:
      summary: Kafka abnormal controller state
      description: No broker in cluster namespace {{ $labels.namespace }} is reporting
        as the active controller for {{ $labels.strimzi_io_controller_name }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: offlinepartitions
    title: OfflinePartitions
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: kafka_controller_kafkacontroller_offlinepartitionscount > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Kafka offline partitions
      description: One or more Kafka partitions have no leader for {{ $labels.strimzi_io_controller_name
        }} on pod {{ $labels.strimzi_io_pod_name }} located on node {{ $labels.node_name
        }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: underminisrpartitioncount
    title: UnderMinIsrPartitionCount
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: kafka_server_replicamanager_underminisrpartitioncount > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Kafka under min ISR partitions
      description: There are {{ $value }} partitions under the min ISR on {{ $labels.kubernetes_pod_name
        }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: offlinelogdirectorycount
    title: OfflineLogDirectoryCount
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: kafka_log_logmanager_offlinelogdirectorycount > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Kafka offline log directories
      description: There are {{ $value }} offline log directories on {{ $labels.kubernetes_pod_name
        }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: clusteroperatorcontainerdown
    title: ClusterOperatorContainerDown
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: (container_last_seen{container="strimzi-cluster-operator"} < time()
          - 120) or absent(container_last_seen{container="strimzi-cluster-operator"})
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: infrastructure
    annotations:
      summary: Strimzi Cluster Operator down
      description: Strimzi Cluster Operator has been down for longer than 2 minutes
        or is missing
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kafkabrokercontainersdown
    title: KafkaBrokerContainersDown
    condition: C
    for: 3m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: absent(container_last_seen{container="kafka",pod=~".+-kafka-[0-9]+"})
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: infrastructure
    annotations:
      summary: All Kafka containers down or in CrashLookBackOff status
      description: All Kafka containers have been down or in CrashLookBackOff status
        for 3 minutes
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: zookeeperavgrequestlatency
    title: ZookeeperAvgRequestLatency
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: zookeeper_avgrequestlatency > 5
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Zookeeper average request latency for last 5 mins.
      description: The average request latency for {{ $labels.pod }} located on node
        {{ $labels.node_name }} exceed 5 seconds for the last 5 minutes. Current value
        is {{ $value }}s
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: zookeeperoutstandingrequests
    title: ZookeeperOutstandingRequests
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: zookeeper_outstandingrequests > 10
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Zookeeper outstanding requests for last 5 mins.
      description: The number of outstanding requests for  {{ $labels.kubernetes_pod_name
        }} localted on node {{ $labels.node_name }} exceed 10 for the last 5 minutes.
        Current value is {{ $value }} requests
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: zookeeperrunningoutofspace
    title: ZookeeperRunningOutOfSpace
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: kubelet_volume_stats_available_bytes{persistentvolumeclaim=~"data-.*-zookeeper-[0-9]"}
          / kubelet_volume_stats_capacity_bytes{persistentvolumeclaim=~"data-.*-zookeeper-[0-9]"}
          * 100 < 30
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Zookeeper disk running out of space. 70% used
      description: "Zookeeper disk running out of space\n Instance =  {{ $labels.instance\
        \ }}\n used = {{ $value }}"
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: predictionzookeeperrunningoutofspace
    title: PredictionZookeeperRunningOutOfSpace
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: predict_linear(kubelet_volume_stats_available_bytes{persistentvolumeclaim=~"data-.*-zookeeper-[0-9]"}[1h],4*3600)
          * (1e-9) < (kubelet_volume_stats_capacity_bytes{persistentvolumeclaim=~"data-.*-zookeeper-[0-9]"}
          * (1e-9)) * 0.2
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: PREDICTION Zookeeper disk running out of space in 4 hours. 80% used
      description: "PREDICTION Zookeeper disk running out of space in 4 hours for\
        \ Instance =  {{ $labels.instance }}\n used = {{ $value }}"
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: zookeepercontainersdown
    title: ZookeeperContainersDown
    condition: C
    for: 3m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: absent(container_last_seen{container="zookeeper",pod=~".+-zookeeper-[0-9]+"})
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: infrastructure
    annotations:
      summary: All `zookeeper` containers in the Zookeeper pods down or in CrashLookBackOff
        status
      description: All `zookeeper` containers in the Zookeeper pods have been down
        or in CrashLookBackOff status for 3 minutes
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: topicoperatorcontainerdown
    title: TopicOperatorContainerDown
    condition: C
    for: 3m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: absent(container_last_seen{container="topic-operator",pod=~".+-entity-operator-.+"})
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: infrastructure
    annotations:
      summary: Container topic-operator in Entity Operator pod down or in CrashLookBackOff
        status
      description: Container topic-operator in Entity Operator pod has been or in
        CrashLookBackOff status for 3 minutes
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: useroperatorcontainerdown
    title: UserOperatorContainerDown
    condition: C
    for: 3m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: absent(container_last_seen{container="user-operator",pod=~".+-entity-operator-.+"})
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: infrastructure
    annotations:
      summary: Container user-operator in Entity Operator pod down or in CrashLookBackOff
        status
      description: Container user-operator in Entity Operator pod have been down or
        in CrashLookBackOff status for 3 minutes
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: entityoperatortlssidecarcontainerdown
    title: EntityOperatorTlsSidecarContainerDown
    condition: C
    for: 3m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: absent(container_last_seen{container="tls-sidecar",pod=~".+-entity-operator-.+"})
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: infrastructure
    annotations:
      summary: Container tls-sidecar Entity Operator pod down or in CrashLookBackOff
        status
      description: Container tls-sidecar in Entity Operator pod have been down or
        in CrashLookBackOff status for 3 minutes
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: underreplicatedpartition
    title: UnderReplicatedPartition
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: kafka_topic_partition_under_replicated_partition > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Topic has under-replicated partitions
      description: Topic  {{ $labels.topic }} has {{ $value }} under-replicated partition
        {{ $labels.partition }}
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: toolargeconsumergrouplag
    title: TooLargeConsumerGroupLag
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: kafka_consumergroup_lag > 100000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Consumer group lag is too big
      description: Consumer group {{ $labels.consumergroup}} lag is too big ({{ $value
        }}) on topic {{ $labels.topic }}/partition {{ $labels.partition }}
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: large-consumer-lag-betgenius
    title: TooLargeConsumerGroupLagBetGeniusConsumer
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: kafka_consumergroup_lag_sum{consumergroup="BetGeniusConsumer_.*"} >
          1000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: infrastructure
    annotations:
      summary: Consumer group lag is too big BetGeniusConsumer
      description: 'Consumer group BetGeniusConsumer: {{ $labels.consumergroup}} lag
        exceed 1000 on topic {{ $labels.topic }}/partition {{ $labels.partition }}.
        Current value: {{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: large-consumer-lag-tournaments
    title: TooLargeConsumerGroupLagPlayerTournaments
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(kafka_consumergroup_lag{consumergroup="player-tournaments"}) by
          (topic) > 3000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Consumer group lag is too big player-tournaments
      description: Consumer group player-tournaments {{ $labels.consumergroup}} lag
        is too big > 3000 ({{ $value }}) on topic {{ $labels.topic }}/partition {{
        $labels.partition }}
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: large-consumer-lag-transaction
    title: TooLargeConsumerGroupLagForTransactionTopic
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: kafka_consumergroup_lag{topic="Transactions",strimzi_io_cluster="ews-transactions-kafka"}
          > 10000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: infrastructure
    annotations:
      summary: Consumer group lag for transactions topic is too big
      description: Consumer group {{ $labels.consumergroup}} lag is too big ({{ $value
        }}) on topic {{ $labels.topic }}/partition {{ $labels.partition }}
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: large-consumer-lag-betradar
    title: TooLargeConsumerGroupLagFeedUpdatesBetradar
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(kafka_consumergroup_lag{topic="feed-updates-betradar"}) > 2000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Consumer lag for feed-updates-betradar is too big
      description: Consumer lag for kafka topic feed-updates-betradar is too big
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: large-consumer-lag-betgenius-feed
    title: TooLargeConsumerGroupLagFeedUpdatesBetgenius
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(kafka_consumergroup_lag{topic="feed-updates-betgenius"}) > 2000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Consumer lag for feed-updates-betgenius is too big
      description: Consumer lag for kafka topic feed-updates-betgenius is too big
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: large-consumer-lag-lsports
    title: TooLargeConsumerGroupLagFeedUpdatesLsports
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(kafka_consumergroup_lag{topic="feed-updates-lsports"}) > 2000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Consumer lag for feed-updates-lsports is too big
      description: Consumer lag for kafka topic feed-updates-lsports is too big
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: toolargeconsumergrouplagnotmappedevents
    title: TooLargeConsumerGroupLagNotMappedEvents
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(kafka_consumergroup_lag{topic="not-mapped-events"}) > 500
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Consumer lag for not-mapped-events is too big
      description: Consumer lag for kafka topic not-mapped-events is too big
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: large-consumer-lag-templates
    title: TooLargeConsumerGroupLagNotMappedTemplates
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(kafka_consumergroup_lag{topic="not-mapped-templates"}) > 500
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Consumer lag for not-mapped-templates is too big
      description: Consumer lag for kafka topic not-mapped-templates is too big
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: large-consumer-lag-parked
    title: TooLargeConsumerGroupLagFeedParkedUpdates
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(kafka_consumergroup_lag{topic="feed-parked-updates"}) > 2000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Consumer lag for feed-parked-updates is too big
      description: Consumer lag for kafka topic feed-parked-updates is too big
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: toolargeconsumergrouplageventmappings
    title: TooLargeConsumerGroupLagEventMappings
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(kafka_consumergroup_lag{topic="event-mappings"}) > 500
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Consumer lag for event-mappings is too big
      description: Consumer lag for kafka topic event-mappings is too big
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: toolargeconsumergrouplagfeedaggregator
    title: TooLargeConsumerGroupLagFeedAggregator
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(kafka_consumergroup_lag{topic="feed-aggregator"}) > 500
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Consumer lag for feed-aggregator is too big
      description: Consumer lag for kafka topic feed-aggregator is too big
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: toolargeconsumergrouplagunifiedfeed
    title: TooLargeConsumerGroupLagUnifiedFeed
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: sum(kafka_consumergroup_lag{topic="unified-feed"}) > 2000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Consumer lag for unified-feed is too big
      description: Consumer lag for kafka topic unified-feed is too big
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: toolargetopiclagforbonuscampaign
    title: TooLargeTopicLagForBonusCampaign
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: kafka_consumergroup_lag{topic=~"strm-BonusCampaignEvents-New|strm-BonusCasinoCampaignEvents|strm-BonusSportCampaignEvents"}
          > 5000
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: infrastructure
    annotations:
      summary: Topic lag for Bonus Events
      description: Topic lag for BonusCampaignEvents|BonusCasinoCampaignEvents|BonusSportCampaignEvents
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kafkaerrorscounterincreased
    title: KafkaErrorsCounterIncreased
    condition: C
    for: 5m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(kafka_errors[1m]) > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Kafka errors counter increased
      description: "Kafka errors counter increased for \n pod = {{ $labels.pod }}\n\
        \ action = {{ $labels.action }} \n  VALUE = {{ $value }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kafka-network-connections-killed
    title: KafkaNetworkSocketServerConnectionsKilled
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: kafka_network_socket_server_connections_killed > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Kafka network socket server connections killed
      description: "Kafka network socket server connections killed for \n pod = {{\
        \ $labels.pod }}\n strimzi_io_coontroller_name = {{ $labels.strimzi_io_coontroller_name\
        \ }} \n  VALUE = {{ $value }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kafkazookeeperauthfailure
    title: KafkaZookeeperAuthFailure
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(kafka_security_aclauthorizer_zookeeperauthfailures_total[1m])
          > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Kafka Zookeeper Auth Failure
      description: "Kafka Zookeeper Auth Failure for \n pod = {{ $labels.kubernetes_pod_name\
        \ }}\n Node: {{ $labels.node_name }} \n strimzi_io_coontroller_name = {{ $labels.strimzi_io_coontroller_name\
        \ }} \n  VALUE = {{ $value }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kafka-broker-failed-fetch-req
    title: KafkaBrokerTopicMetricsFailedFetchRequests
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(kafka_server_brokertopicmetrics_failedfetchrequests_total[1m])
          > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Kafka Broker Topic Metrics Failed Fetch Requests
      description: "Kafka Broker Topic Metrics Failed Fetch Requests for \n pod =\
        \ {{ $labels.pod }}\n strimzi_io_coontroller_name = {{ $labels.strimzi_io_coontroller_name\
        \ }} \n  VALUE = {{ $value }}\n Node: {{ $labels.node_name }}"
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kafka-broker-failed-producer-req
    title: KafkaBrokerTopicMetricsFailedProducerRequests
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(kafka_server_brokertopicmetrics_failedproducerequests_total[1m])
          > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Kafka Broker Topic Metrics Failed Producer Requests
      description: "Kafka Broker Topic Metrics Failed Producer Requests for \n pod\
        \ = {{ $labels.pod }}\n strimzi_io_coontroller_name = {{ $labels.strimzi_io_coontroller_name\
        \ }} \n  VALUE = {{ $value }}\n Node: {{ $labels.node_name }}"
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kafka-broker-invalid-magic-num
    title: KafkaBrokerTopicMetricsInvalidMagicNumberRecords
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(kafka_server_brokertopicmetrics_invalidmagicnumberrecords_total[1m])
          > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Kafka Broker Topic Metrics Invalid Magic Number Records
      description: "Kafka Broker Topic Metrics Invalid Magic Number Records for \n\
        \ pod = {{ $labels.pod }}\n strimzi_io_coontroller_name = {{ $labels.strimzi_io_coontroller_name\
        \ }} \n  VALUE = {{ $value }}\n Node: {{ $labels.node_name }}"
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kafka-broker-invalid-msg-crc
    title: KafkaBrokerTopicMetricsInvalidMessageCrcRecords
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(kafka_server_brokertopicmetrics_invalidmessagecrcrecords_total[1m])
          > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Kafka Broker Topic Metrics Invalid Message CRC Records Total
      description: "Kafka Broker Topic Metrics Invalid Message Size Records for \n\
        \ pod = {{ $labels.pod }}\n strimzi_io_coontroller_name = {{ $labels.strimzi_io_coontroller_name\
        \ }} \n  VALUE = {{ $value }}\n Node: {{ $labels.node_name }}"
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kafka-broker-invalid-offset
    title: KafkaBrokerTopicMetricsInvalidOffsetRecords
    condition: C
    for: 0s
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: increase(kafka_server_brokertopicmetrics_invalidoffsetorsequencerecords_total[1m])
          > 0
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: warning
      department: DevOps
      component: infrastructure
    annotations:
      summary: Kafka Broker Topic Metrics Invalid Offset Or Sequqnce Records
      description: "Kafka Broker Topic Metrics Invalid Offset Records for \n pod =\
        \ {{ $labels.pod }}\n strimzi_io_coontroller_name = {{ $labels.strimzi_io_coontroller_name\
        \ }} \n  VALUE = {{ $value }}\n Node: {{ $labels.node_name }}"
      value: '{{ $value }}'
    noDataState: NoData
    execErrState: Error
    isPaused: false
  - uid: kafkabrokerstateunhealthy
    title: KafkaBrokerStateUnhealthy
    condition: C
    for: 10m
    data:
    - refId: A
      relativeTimeRange:
        from: 300
        to: 0
      datasourceUid: prometheus
      model:
        datasource:
          type: prometheus
          uid: prometheus
        editorMode: code
        expr: kafka_server_kafkaserver_brokerstate != 3
        instant: true
        intervalMs: 1000
        legendFormat: __auto
        maxDataPoints: 43200
        range: false
        refId: A
    labels:
      severity: critical
      department: DevOps
      component: infrastructure
    annotations:
      summary: Kafka Broker State Unhealthy
      description: "Kafka Broker State Unhealthy: \n pod = {{ $labels.kubernetes_pod_name\
        \ }}\n strimzi_io_coontroller_name = {{ $labels.strimzi_io_coontroller_name\
        \ }} \n  VALUE = {{ $value }}\n Node: {{ $labels.node_name }}"
    noDataState: NoData
    execErrState: Error
    isPaused: false
